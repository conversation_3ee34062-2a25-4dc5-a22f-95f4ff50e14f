#!/usr/bin/env python3
"""
调试所有目标表的行，查看哪些包含99991231
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.vectorization.vector_store import ChromaVectorStore
import re

def debug_all_target_rows():
    """调试所有目标表的行"""
    
    # 初始化向量存储
    row_vs = ChromaVectorStore(collection_name="row_vectors")
    
    # 表名关键词
    table_keywords = ['自营资金业务余额表']
    
    print(f"查找包含表名关键词的所有行: {table_keywords}")
    
    # 获取所有行的元数据，按表名筛选
    all_count = row_vs.collection.count()
    print(f"总共有 {all_count} 行")
    
    # 分批获取所有行的元数据
    batch_size = 1000
    all_target_rows = []
    
    for offset in range(0, all_count, batch_size):
        batch_size_actual = min(batch_size, all_count - offset)
        batch_ret = row_vs.collection.get(
            limit=batch_size_actual,
            offset=offset,
            include=["metadatas"]
        )
        
        if batch_ret.get("ids"):
            batch_ids = batch_ret["ids"]
            batch_metas = batch_ret["metadatas"]
            
            # 筛选包含目标表名的行
            for rid, meta in zip(batch_ids, batch_metas):
                text = meta.get("text", "")
                for keyword in table_keywords:
                    table_name_pattern = f"表名:CBRC_EAST5_0_业务Mapping_V2_2_4_{keyword}"
                    if table_name_pattern in text:
                        all_target_rows.append({
                            "id": rid,
                            "sheet_id": meta.get("sheet_id"),
                            "rowid": meta.get("rowid"),
                            "text": text
                        })
                        break
    
    print(f"找到 {len(all_target_rows)} 个目标表的行")
    
    # 检查哪些行包含99991231
    rows_with_99991231 = []
    for row in all_target_rows:
        if "99991231" in row["text"]:
            rows_with_99991231.append(row)
    
    print(f"\n包含99991231的行: {len(rows_with_99991231)} 个")
    
    for i, row in enumerate(rows_with_99991231):
        sheet_id = row["sheet_id"]
        rowid = row["rowid"]
        text = row["text"]
        
        print(f"\n{i+1}. sheet_id={sheet_id}, rowid={rowid}")
        print(f"   完整文本: {text}")
        
        # 提取字段名
        if "数据项名称:" in text:
            field_name = text.split("数据项名称:")[1].split("|")[0].strip()
            print(f"   🎯 字段名: {field_name}")
    
    # 显示所有目标表的行（前10个）
    print(f"\n所有目标表的行（前10个）:")
    for i, row in enumerate(all_target_rows[:10]):
        sheet_id = row["sheet_id"]
        rowid = row["rowid"]
        text = row["text"]
        
        print(f"\n{i+1}. sheet_id={sheet_id}, rowid={rowid}")
        print(f"   文本: {text[:150]}...")
        
        # 检查是否包含99991231
        if "99991231" in text:
            print(f"   🎯 包含99991231！")

if __name__ == "__main__":
    debug_all_target_rows()
