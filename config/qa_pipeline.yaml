fixed_steps:
  - name: intent_classifier
    config:
      provider: gientech
      model: Qwen3-30B-A3B
      think: false
      min_prob: 0.3
      prompt: |
        你是一个查询意图分类器, 识别和理解用户输入的问题，进行分类。
        我们的数据是监管领域的知识数据，存储在多个Excel文件中，每个文件分为多个
        sheet，每个sheet中存储表格分为多个row。
        文件通常是不同业务领域的数据上报映射关系，比如：一表通、EAST等。
        每个sheet存储该上报业务对应的不同表，比如：G01资产负债项目统计表、个人信贷分账户明细等。
        每个表中的row，通常对应具体的字段信息, 比如：明细科目编号、科目名称等。

        要求返回一个 JSON 对象，
        包含 file、sheet、row 三个键，各自对应概率 (0-1 之间，且和为 1)。
        仅回复 JSON，无需解释。 比如：{"file":0.1,"sheet":0.2,"row":0.7}
  - name: row_vector_recall
    config:
      top_k_rows: 20  # 行级向量召回数量
      top_n_rows: 5   # 最终返回的行数
      kw_weight: 0.3  # 关键词权重
  - name: vector_recall
    config:
      topk: 20   # 每层级最多召回 20 条向量
      embedding_provider: ollama
      embedding_model: dengcao/Qwen3-Embedding-4B:Q5_K_M
  - name: rerank
    config:
      top_k: 5
      provider: siliconflow
      model: Qwen/Qwen3-Reranker-4B
  - name: llm_answer
    config:
      provider: gientech
      model: Qwen3-30B-A3B 
      think: true
      llm_decide_preview: false
      system_prompt: |
        你是一位中文数据分析助手，回答应精准、简洁。若上下文不足以回答，应如实说明。不要返回大量原始表数据信息。
        最终输出的答案不要超过 300 个字。 