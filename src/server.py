from __future__ import annotations
from fastapi import FastAPI
from pydantic import BaseModel
import logging

from .orchestration.dynamic_orchestrator import DynamicOrchestrator
from .utils.logger import setup_logging

"""FastAPI Server：提供 /ask 接口供外部访问智能问答功能。"""

# 配置日志只输出到文件，不输出到console
setup_logging(log_file="logs/qa_server.log", level=logging.INFO)

app = FastAPI(title="Hierarchical Data QA Server")

_orc = DynamicOrchestrator()


class AskRequest(BaseModel):  # noqa: D101
    question: str


@app.post("/ask")  # noqa: D401
async def ask(req: AskRequest):  # noqa: D401
    """接收用户问题，返回检索上下文与示例表格。"""
    result = _orc.answer(req.question)
    return {"ok": True, "data": result} 