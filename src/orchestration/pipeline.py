"""Pipeline 构建与执行逻辑，支持 Fixed + Dynamic 组件。"""
from __future__ import annotations

from pathlib import Path
from typing import Dict, List, Any

import yaml  # type: ignore
import importlib, pkgutil, sys

from .registry import ComponentRegistry

# ------------------------------------------------------------------
# Auto-load components package to ensure registration when running CLI
# ------------------------------------------------------------------


def _autoload_components() -> None:  # noqa: D401
    """自动加载组件包，支持多个包路径。"""

    # 支持的包列表，可按需扩展
    packages = [
        "src.orchestration.components",  # 旧版路径
        "src.qa_pro.components",        # 新版路径
    ]

    for package_name in packages:
        try:
            pkg = importlib.import_module(package_name)
        except ModuleNotFoundError:
            # 包不存在时跳过，不抛异常以兼容不同部署场景
            continue

        pkg_path = Path(pkg.__file__).parent  # type: ignore[attr-defined]
        for module in pkgutil.iter_modules([str(pkg_path)]):
            full_name = f"{package_name}.{module.name}"
            if full_name not in sys.modules:
                importlib.import_module(full_name)

# ----------------------------------------------------------------------
# Helper
# ----------------------------------------------------------------------


def _load_yaml(path: str | Path) -> Dict[str, Any]:  # noqa: D401
    """轻量 YAML 加载。不存在返回空 dict。"""
    p = Path(path)
    if not p.exists():
        return {}
    with p.open("r", encoding="utf-8") as f:
        return yaml.safe_load(f) or {}


# ----------------------------------------------------------------------
# MixedPipeline
# ----------------------------------------------------------------------


class MixedPipeline:  # noqa: D101
    def __init__(self, cfg_path: str | Path = "config/pipeline.yaml") -> None:  # noqa: D401
        self._cfg_path = Path(cfg_path)
        self._cfg: Dict[str, Any] = _load_yaml(self._cfg_path)
        self.fixed_cfg: List[Dict[str, Any]] = self._cfg.get("fixed_steps", [])
        self.candidates: List[Dict[str, Any]] = self._cfg.get("dynamic_candidates", [])
        self.planner_cfg: Dict[str, Any] = self._cfg.get("planner", {})
        self.components: List[Any] = []

    # ------------------------------------------------------------------
    # Build & Execute
    # ------------------------------------------------------------------
    def build(self, user_query: str) -> None:  # noqa: D401
        """根据配置 + LLMPlanner 构建组件实例列表。"""
        _autoload_components()
        # 1) build fixed parts
        fixed_parts = [
            ComponentRegistry.get(conf["name"])(conf.get("config", {}))  # type: ignore[arg-type]
            for conf in self.fixed_cfg
        ]

        # 2) dynamic plan via LLMPlanner (fallback to empty) ----------------------------------
        dynamic_plan: List[Dict[str, Any]] = []
        try:
            from .llm_planner import LLMPlanner  # type: ignore  # noqa: WPS433 (intra-package import)

            dynamic_plan = LLMPlanner(self.candidates, **self.planner_cfg).plan(user_query)
        except ModuleNotFoundError:
            # Planner 未实现时采用空动态流程
            dynamic_plan = []
        except Exception as ex:  # noqa: BLE001
            # Planner 失败时可降级
            print(f"[MixedPipeline] Planner failed: {ex}")
            dynamic_plan = []

        # 3) ensure required components
        required_names = {c["name"] for c in self.candidates if c.get("required")}
        present_names = {step["name"] for step in dynamic_plan}
        missing_required = required_names - present_names
        for name in missing_required:
            dynamic_plan.append({"name": name})

        # 4) Reorder dynamic_plan to follow the order defined in `self.candidates`
        ordered_dynamic_plan: List[Dict[str, Any]] = []
        seen: set[str] = set()

        # first, keep candidates order
        for cand in self.candidates:
            name = cand["name"]
            for step in dynamic_plan:
                if step["name"] == name and name not in seen:
                    ordered_dynamic_plan.append(step)
                    seen.add(name)

        # then, append any additional steps not in candidates (edge cases)
        for step in dynamic_plan:
            if step["name"] not in seen:
                ordered_dynamic_plan.append(step)
                seen.add(step["name"])

        # 5) build dynamic component instances
        dynamic_parts = [
            ComponentRegistry.get(conf["name"])(conf.get("config", {}))  # type: ignore[arg-type]
            for conf in ordered_dynamic_plan
        ]

        self.components = fixed_parts + dynamic_parts

    # ------------------------------------------------------------------
    def execute(self, payload: Dict[str, Any]) -> Dict[str, Any]:  # noqa: D401
        if not self.components:
            raise RuntimeError("Pipeline 未构建，请先调用 build()")
        data = dict(payload)
        for comp in self.components:
            data.update(comp.run(data))
        return data 