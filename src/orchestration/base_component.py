"""Base classes for pipeline components."""
from __future__ import annotations

from abc import ABC, abstractmethod
from typing import Any, Dict, List

from .registry import ComponentRegistry

__all__ = [
    "BaseComponent",
    "register_component",
]


class BaseComponent(ABC):  # noqa: D101
    """所有可插拔组件的抽象基类。"""

    # 每个组件都必须声明唯一的 name，用于注册与查找
    name: str = "base"

    # 可选：声明前置输入/输出字段，便于校验与可视化
    inputs: List[str] = []
    outputs: List[str] = []

    def __init__(self, config: Dict[str, Any] | None = None) -> None:  # noqa: D401
        self.config: Dict[str, Any] = config or {}
        # 供子类在 setup 中完成模型加载、连接初始化等重量级操作
        self.setup(self.config)

    # ------------------------------------------------------------------
    # Lifecycle hooks
    # ------------------------------------------------------------------
    def setup(self, config: Dict[str, Any]) -> None:  # noqa: D401
        """子类可覆写以执行初始化逻辑。"""

    @abstractmethod
    def run(self, payload: Dict[str, Any], **kwargs: Any) -> Dict[str, Any]:  # noqa: D401,E501
        """核心执行逻辑，读取 `payload` 并返回增量字典。"""


# ----------------------------------------------------------------------
# Helper decorator for easy registration
# ----------------------------------------------------------------------

def register_component(cls: type[BaseComponent]) -> type[BaseComponent]:  # noqa: D401
    """类装饰器：自动将组件注册到 ComponentRegistry。"""

    if not hasattr(cls, "name") or cls.name == "base":  # noqa: WPS421
        raise ValueError("组件必须定义唯一的 `name` 字段才能注册")

    ComponentRegistry.register(cls)
    return cls 