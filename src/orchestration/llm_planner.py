"""LLMPlanner 简化实现：暂以规则模拟大模型输出。"""
from __future__ import annotations

from typing import List, Dict, Any


class LLMPlanner:  # noqa: D101
    """LLMPlanner: 根据候选组件列表与用户问题，返回需要执行的组件 sequence。

    默认使用规则 Mock；当 cfg `llm_provider != 'mock'` 时，会调用 parser.llm.client.ask，
    让大模型输出 JSON 数组（元素可为组件名或对象）。
    """

    _DEFAULT_SYSTEM_PROMPT = (
        "你是一个流程编排器，下面给出可选组件列表，请结合用户问题挑选最合适的组件并保证顺序。\n"
        "要求：\n"
        "1. 输出 JSON 数组，每个元素是组件名称或包含 name/params 的对象。\n"
        "2. 如果答案无需访问数据库，可仅返回 ['llm_answer']。"
    )

    def __init__(self, candidates: List[Dict[str, Any]], **llm_kw: Any) -> None:  # noqa: D401
        self.candidates = candidates
        self.llm_provider = llm_kw.get("llm_provider", "mock").lower()
        self.temperature = llm_kw.get("temperature", 0.0)
        self.system_prompt = llm_kw.get("system_prompt", self._DEFAULT_SYSTEM_PROMPT)

    # ------------------------------------------------------------------
    def plan(self, query: str) -> List[Dict[str, Any]]:  # noqa: D401
        if self.llm_provider == "mock":
            return self._mock_plan(query)
        try:
            from src.parser.llm.client import ask, LLMClientError  # type: ignore
            import json

            # 构造组件描述，供大模型参考
            comp_lines = [f"- {c['name']}: {c.get('description', '')}" for c in self.candidates]
            comp_desc = "\n".join(comp_lines)
            user_prompt = (
                f"用户问题：{query}\n"
                f"可选组件：\n{comp_desc}\n"
                "请按需要返回组件列表(JSON)，例如: [\"vector_recall\", {\"name\": \"row_lookup\"}]"
            )

            rsp = ask(
                user_prompt,
                system=self.system_prompt,
                temperature=self.temperature,
                response_format={"type": "json_array"},
            )

            try:
                data = json.loads(rsp)
            except json.JSONDecodeError:
                # 尝试从文中提取 JSON
                import re

                match = re.search(r"\[[\s\S]*?\]", rsp)
                if not match:
                    raise ValueError("LLM response not JSON array")
                data = json.loads(match.group(0))

            plan: List[Dict[str, Any]] = []
            for elem in data:
                if isinstance(elem, str):
                    plan.append({"name": elem})
                elif isinstance(elem, dict) and "name" in elem:
                    plan.append(elem)
            if plan:
                return plan
        except (ImportError, LLMClientError, Exception):  # noqa: BLE001
            pass  # fallthrough to mock
        # fallback
        return self._mock_plan(query)

    # ------------------------------------------------------------------
    def _mock_plan(self, query: str) -> List[Dict[str, Any]]:  # noqa: D401
        q_lower = query.lower()
        # NEW: detect sql-like queries
        sql_keywords = ["统计", "总数", "平均", "select", "where", "count(", "sum("]
        if any(k in q_lower for k in sql_keywords):
            plan_names = ["list_tables", "describe_table", "sql_generate", "sql_execute", "llm_answer"]
            return [{"name": n} for n in plan_names if any(c["name"] == n for c in self.candidates)]

        plan: List[Dict[str, Any]] = []
        if any(k in q_lower for k in ["汇总", "整体", "整表", "表概况", "表级", "sheet summary", "总结", "概括"]):
            for name in ["vector_recall", "sheet_meta", "llm_answer"]:
                if any(c["name"] == name for c in self.candidates):
                    plan.append({"name": name})
        else:
            for name in ["vector_recall", "row_lookup", "rerank", "mcp_query", "llm_answer"]:
                if any(c["name"] == name for c in self.candidates):
                    plan.append({"name": name})
        return plan 