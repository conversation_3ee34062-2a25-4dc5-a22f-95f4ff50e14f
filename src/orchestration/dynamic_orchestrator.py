"""新的 Orchestrator：使用 MixedPipeline 动态编排组件。"""
from __future__ import annotations

from typing import Dict, Any

from .pipeline import MixedPipeline


class DynamicOrchestrator:  # noqa: D101
    def __init__(self, cfg_path: str = "config/pipeline.yaml") -> None:  # noqa: D401
        self.pipeline = MixedPipeline(cfg_path)

    # ------------------------------------------------------------------
    def answer(self, question: str) -> Dict[str, Any]:  # noqa: D401
        """对外主入口，返回包含 answer/tables 等字段的 dict。"""
        # 1. 构建 Pipeline
        self.pipeline.build(question)
        # 2. 执行
        result = self.pipeline.execute({"query": question})
        # 若 answer 字段不存在，仅返回占位符文本，避免在回答中展示表预览信息
        # 表预览信息如有仍会存在於 `tables_preview` 字段，由上层界面按需渲染。
        if "answer" not in result:
            result["answer"] = "(无答案)"
        return result 