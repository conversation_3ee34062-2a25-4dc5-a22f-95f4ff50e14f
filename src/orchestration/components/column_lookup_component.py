from __future__ import annotations

"""ColumnLookupComponent: 读取 query_vector，在 Chroma 中检索 column_constant 级别向量，
根据返回 id 查询 SQLite column_vectors.text，返回文本列表供 LLM 使用。"""

from typing import Dict, Any, List, Tuple
import sqlite3
import logging

from ..base_component import BaseComponent, register_component
from src.vectorization.vector_store import ChromaVectorStore


@register_component
class ColumnLookupComponent(BaseComponent):  # noqa: D101
    name = "column_lookup"
    inputs = ["query_vector"]
    outputs = ["column_texts"]

    # ------------------------------------------------------------------
    def setup(self, config: Dict[str, Any]) -> None:  # noqa: D401
        self.store = ChromaVectorStore()
        self.top_k: int = config.get("top_k", 5)
        self.db_path: str = config.get("db_path", "output/data.db")
        self.logger = logging.getLogger(self.name)

    # ------------------------------------------------------------------
    def run(self, payload: Dict[str, Any], **kwargs: Any) -> Dict[str, Any]:  # noqa: D401
        vec: List[float] | None = payload.get("query_vector")
        if not vec:
            return {}

        ids = self.store.query(vec, top_k=self.top_k, filter={"level": "column_constant"})
        if not ids:
            self.logger.debug("No column_constant ids retrieved")
            return {}

        self.logger.debug("Column recall ids: %s", ids)
        # Parse ids to (table_name, column_name)
        pairs: List[Tuple[str, str]] = []
        for cid in ids:
            # expected format: const:table_name:column_name
            parts = cid.split(":", 2)
            if len(parts) != 3 or parts[0] != "const":
                continue
            table_name, column_name = parts[1], parts[2]
            pairs.append((table_name, column_name))

        if not pairs:
            return {}

        # Build SQL to fetch text from column_vectors
        placeholders = " OR ".join("(table_name=? AND column_name=?)" for _ in pairs)
        params: List[Any] = []
        for tbl, col in pairs:
            params.extend([tbl, col])
        sql = f"SELECT table_name, column_name, text FROM column_vectors WHERE {placeholders}"
        self.logger.debug("Column lookup SQL: %s | params=%s", sql, params)

        texts: List[Dict[str, Any]] = []
        try:
            conn = sqlite3.connect(self.db_path)
            cur = conn.execute(sql, params)
            for tbl, col, text in cur.fetchall():
                texts.append({
                    "table_name": tbl,
                    "column_name": col,
                    "text": text,
                })
            conn.close()
        except Exception as exc:  # noqa: BLE001
            self.logger.exception("Column lookup failed: %s", exc)
            return {}

        if not texts:
            return {}

        self.logger.info("Column lookup returned %s texts", len(texts))
        return {"column_texts": texts} 