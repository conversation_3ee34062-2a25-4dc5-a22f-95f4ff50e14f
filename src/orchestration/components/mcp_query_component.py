"""根据 sheet_ids 查询表并返回预览数据。"""
from __future__ import annotations

from typing import Dict, Any, List
import sqlite3
from sqlalchemy.exc import SQLAlchemyError  # type: ignore
import pandas as pd  # type: ignore

from ..base_component import BaseComponent, register_component
from src.mcp.client import MCPClient, QueryTable


@register_component
class MCPQueryComponent(BaseComponent):  # noqa: D101
    name = "mcp_query"
    inputs = ["sheet_ids"]
    outputs = ["tables_preview"]

    def setup(self, config: Dict[str, Any]) -> None:  # noqa: D401
        self.mcp = MCPClient()
        self.limit: int = config.get("limit", 10)
        # SQLite 路径可通过 config 指定
        self.db_path: str = config.get("db_path", "output/data.db")
        import logging
        self.logger = logging.getLogger(self.name)

    # ------------------------------------------------------------------
    def run(self, payload: Dict[str, Any], **kwargs: Any) -> Dict[str, Any]:  # noqa: D401
        sheet_ids: List[str] = payload.get("sheet_ids", [])
        self.logger.debug("MCP query start | sheet_ids=%s", sheet_ids)
        tables: Dict[str, List[Dict[str, Any]]] = {}
        for sid in sheet_ids:
            try:
                _, sid_str = sid.split(":", 1)
                sid_int = int(sid_str)
            except Exception:
                self.logger.warning("Invalid sheet_id format skipped: %s", sid)
                continue
            # 从 sheet_metadata 取表名
            table_name = self._get_table_name(sid_int)
            if not table_name:
                self.logger.debug("No table name found for sheet_id %s", sid_int)
                continue
            qt = QueryTable(table_name=table_name, limit=self.limit)
            try:
                df = self.mcp.query_table(qt)
            except SQLAlchemyError:
                # 表不存在或查询失败，跳过但不中断整个 pipeline
                self.logger.exception("MCP query failed for table %s", table_name)
                continue
            tables[table_name] = df.head(self.limit).to_dict(orient="records")
            self.logger.info("Queried table %s rows=%s", table_name, len(tables[table_name]))
        self.logger.debug("MCP query finished, total tables=%s", len(tables))
        return {"tables_preview": tables}

    # ------------------------------------------------------------------
    def _get_table_name(self, sheet_id: int) -> str | None:  # noqa: D401
        try:
            conn = sqlite3.connect(self.db_path)
            # 优先使用 table_name，若为空再退回 sheet_name
            cur = conn.execute(
                "SELECT COALESCE(table_name, sheet_name) FROM sheet_metadata WHERE sheet_id = ? LIMIT 1",
                (sheet_id,),
            )
            row = cur.fetchone()
            table_name = str(row[0]) if row else None

            # 如果表不存在（可能因前缀等差异），尝试在 row_vectors 中查找实际 table_name
            if table_name and not self._table_exists(conn, table_name):
                self.logger.debug("Table %s not found, fallback search row_vectors", table_name)
                cur2 = conn.execute(
                    "SELECT DISTINCT table_name FROM row_vectors WHERE sheet_id = ? LIMIT 1", (sheet_id,)
                )
                row2 = cur2.fetchone()
                if row2:
                    table_name = str(row2[0])

            conn.close()
            if table_name and self._table_exists(sqlite3.connect(self.db_path), table_name):
                return table_name
        except Exception:  # noqa: BLE001
            pass
        return None

    # ------------------------------------------------------------------
    def _table_exists(self, conn: sqlite3.Connection, name: str) -> bool:  # noqa: D401
        cur = conn.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (name,))
        return cur.fetchone() is not None 