from __future__ import annotations

"""SheetMetaComponent: 根据 sheet_ids 查询 sheet_metadata 描述信息。"""

from typing import Dict, Any, List
import sqlite3
import logging

from ..base_component import BaseComponent, register_component


@register_component
class SheetMetaComponent(BaseComponent):  # noqa: D101
    name = "sheet_meta"
    inputs = ["sheet_ids"]
    outputs = ["sheet_meta"]

    # ------------------------------------------------------------------
    def setup(self, config: Dict[str, Any]) -> None:  # noqa: D401
        # SQLite db 路径可从 config 指定，默认为 output/data.db
        self.db_path: str = config.get("db_path", "output/data.db")
        self.logger = logging.getLogger(self.name)

    # ------------------------------------------------------------------
    def run(self, payload: Dict[str, Any], **kwargs: Any) -> Dict[str, Any]:  # noqa: D401
        sheet_ids: List[str] = payload.get("sheet_ids", [])
        if not sheet_ids:
            return {}

        try:
            ids_num = [int(s.split(":", 1)[1]) for s in sheet_ids]
        except (IndexError, ValueError):
            self.logger.debug("Invalid sheet_ids format, skip sheet_meta lookup: %s", sheet_ids)
            return {}

        placeholders = ",".join("?" for _ in ids_num)
        sql = (
            # 新 schema: description 存储在 sheet_attributes 表中，key = 'description'
            f"SELECT sm.sheet_id, sm.sheet_name, sa.value AS description "
            f"FROM sheet_metadata sm "
            f"LEFT JOIN sheet_attributes sa ON sm.sheet_id = sa.sheet_id AND sa.key = 'description' "
            f"WHERE sm.sheet_id IN ({placeholders})"
        )
        self.logger.debug("Sheet meta SQL: %s | params=%s", sql, ids_num)

        rows: List[Dict[str, Any]] = []
        try:
            conn = sqlite3.connect(self.db_path)
            cur = conn.execute(sql, ids_num)
            for sid, name, desc in cur.fetchall():
                rows.append({
                    "sheet_id": sid,
                    "sheet_name": name,
                    "description": desc,
                })
            conn.close()
        except Exception as exc:  # noqa: BLE001
            self.logger.exception("Sheet meta lookup failed: %s", exc)
            return {}

        if not rows:
            return {}

        self.logger.debug("Sheet meta fetched: %s", rows)
        return {"sheet_meta": rows} 