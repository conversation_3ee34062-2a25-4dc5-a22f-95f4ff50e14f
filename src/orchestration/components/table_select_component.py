"""TableSelectComponent：根据问题从表名列表中选出最相关的一张表。"""
from __future__ import annotations

from typing import Any, Dict, List
import json

from src.orchestration.base_component import BaseComponent, register_component
from src.parser.llm.client import ask, LLMClientError, _extract_json_block  # type: ignore


@register_component
class TableSelectComponent(BaseComponent):  # noqa: D101
    name = "table_select"

    inputs: List[str] = ["query", "tables"]
    outputs: List[str] = ["selected_table"]

    _DEFAULT_PROMPT = (
        "你是数据库专家，任务：根据用户问题，从给定表名列表中挑选最相关的一张表。\n"
        "步骤：\n"
        "1. 思考用户查询需要哪些业务数据；\n"
        "2. 从 tables 列表中选出最符合的表名；\n"
        "仅回复 JSON，如：{\"table\": \"表名\"}，不要额外解释。"
    )

    # ------------------------------------------------------------------
    def setup(self, config: Dict[str, Any]) -> None:  # noqa: D401
        self.llm_provider = config.get("provider", "mock")
        self.model = config.get("model")
        self.temperature = config.get("temperature", 0)
        self.prompt_tpl: str = config.get("prompt", self._DEFAULT_PROMPT)
        self.think: bool | None = config.get("think")

    # ------------------------------------------------------------------
    def run(self, payload: Dict[str, Any], **kwargs: Any) -> Dict[str, Any]:  # noqa: D401
        question: str = payload.get("query", "")
        tables: List[str] = payload.get("tables", [])
        if not question or not tables:
            return {}

        # mock provider: 简单 keyword 匹配或取第一个
        if self.llm_provider == "mock":
            q_lower = question.lower()
            # 尝试找到包含关键词的表
            for tbl in tables:
                if any(tok in tbl.lower() for tok in ["loan", "合同", "contract", "bank", "机构", "codes", "code"]):
                    return {"selected_table": tbl}
            return {"selected_table": tables[0]}

        tables_json = json.dumps(tables, ensure_ascii=False)
        prompt = (
            f"{self.prompt_tpl}\n\n可选表名列表：\n{tables_json}\n\n用户问题：{question}\n"
            "请仅输出 JSON。"
        )
        try:
            raw = ask(
                prompt,
                provider=self.llm_provider,
                model=self.model,
                temperature=self.temperature,
                think=self.think,
                response_format={"type": "json_object"},
            )
            json_text = _extract_json_block(raw)
            data = json.loads(json_text)
            sel_table = str(data.get("table"))
            if sel_table and sel_table in tables:
                return {"selected_table": sel_table}
        except (LLMClientError, ValueError, json.JSONDecodeError):
            pass
        # fallback to first table
        return {"selected_table": tables[0]} 