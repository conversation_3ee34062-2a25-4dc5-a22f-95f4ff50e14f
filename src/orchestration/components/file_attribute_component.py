from __future__ import annotations

"""FileAttributeComponent: 根据 retrieved_ids 中的 file 级向量 id，查询 file_attributes 表的文本。"""

from typing import Dict, Any, List
import sqlite3
import logging

from ..base_component import BaseComponent, register_component


@register_component
class FileAttributeComponent(BaseComponent):  # noqa: D101
    name = "file_attribute"
    inputs = ["retrieved_ids"]
    outputs = ["file_texts"]

    # ------------------------------------------------------------------
    def setup(self, config: Dict[str, Any]) -> None:  # noqa: D401
        # SQLite db 路径可从 config 指定，默认为 output/data.db
        self.db_path: str = config.get("db_path", "output/data.db")
        self.logger = logging.getLogger(self.name)

    # ------------------------------------------------------------------
    def run(self, payload: Dict[str, Any], **kwargs: Any) -> Dict[str, Any]:  # noqa: D401
        ids: List[str] = payload.get("retrieved_ids", [])
        if not ids:
            return {}

        file_keys: List[tuple[int, str]] = []  # (file_id, key)
        for fid in ids:
            # 格式: file:{file_id}:{key}
            parts = fid.split(":", 2)
            if len(parts) == 3 and parts[0] == "file":
                try:
                    file_id = int(parts[1])
                    key = parts[2]
                    file_keys.append((file_id, key))
                except ValueError:
                    continue

        if not file_keys:
            return {}

        placeholders = " OR ".join("(file_id=? AND key=?)" for _ in file_keys)
        params: List[Any] = []
        for fid, key in file_keys:
            params.extend([fid, key])
        sql = f"SELECT file_id, key, value FROM file_attributes WHERE {placeholders}"
        self.logger.debug("FileAttribute SQL: %s | params=%s", sql, params)

        texts: List[Dict[str, Any]] = []
        try:
            conn = sqlite3.connect(self.db_path)
            cur = conn.execute(sql, params)
            for file_id, key, value in cur.fetchall():
                texts.append({
                    "file_id": file_id,
                    "key": key,
                    "text": value,
                })
            conn.close()
        except Exception as exc:  # noqa: BLE001
            self.logger.exception("File attribute lookup failed: %s", exc)
            return {}

        if not texts:
            return {}

        self.logger.info("File attribute lookup returned %s texts", len(texts))
        return {"file_texts": texts} 