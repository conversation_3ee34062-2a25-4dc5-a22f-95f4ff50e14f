from __future__ import annotations

"""RowLookupComponent：根据 row-level IDs 查询 row_vectors.text。"""

from typing import Dict, Any, List, Tuple
import sqlite3
import logging

from ..base_component import BaseComponent, register_component


@register_component
class RowLookupComponent(BaseComponent):  # noqa: D101
    name = "row_lookup"
    inputs = ["retrieved_ids"]
    outputs = ["row_texts"]

    # ------------------------------------------------------------------
    def setup(self, config: Dict[str, Any]) -> None:  # noqa: D401
        # SQLite db 路径可从 config 指定，默认为 output/data.db
        self.db_path: str = config.get("db_path", "output/data.db")
        self.limit: int = config.get("limit", 20)
        self.logger = logging.getLogger(self.name)

    # ------------------------------------------------------------------
    def run(self, payload: Dict[str, Any], **kwargs: Any) -> Dict[str, Any]:  # noqa: D401
        ids: List[str] = payload.get("retrieved_ids") or payload.get("sheet_ids", [])
        row_keys: List[Tuple[int, int]] = []  # (sheet_id, rowid)
        for rid in ids:
            try:
                sheet_part, row_part = rid.split(":", 1)
                sheet_id = int(sheet_part)
                row_id = int(row_part)
                row_keys.append((sheet_id, row_id))
            except ValueError:
                # 不是行级 id，跳过
                continue
        if not row_keys:
            self.logger.debug("No row-level ids found, skip row lookup")
            return {}

        placeholders = ",".join(["(?,?)"] * len(row_keys))
        params: List[Any] = []
        for s, r in row_keys:
            params.extend([s, r])

        sql = (
            f"SELECT sheet_id, rowid, text FROM row_vectors "
            f"WHERE (sheet_id, rowid) IN ({placeholders}) LIMIT ?"
        )
        params.append(self.limit)

        self.logger.debug("Row lookup SQL: %s | params=%s", sql, params)

        fetched: List[Dict[str, Any]] = []
        try:
            conn = sqlite3.connect(self.db_path)
            cur = conn.execute(sql, params)
            for sheet_id, rowid, text in cur.fetchall():
                fetched.append({
                    "sheet_id": sheet_id,
                    "rowid": rowid,
                    "text": text,
                })
            conn.close()
        except Exception as exc:  # noqa: BLE001
            self.logger.exception("Row lookup failed: %s", exc)
            return {}

        # 根据原始 ids 顺序重新排序结果，便于后续组件直接使用
        order_map = {rid: idx for idx, rid in enumerate(ids)}
        fetched.sort(key=lambda r: order_map.get(f"{r['sheet_id']}:{r['rowid']}", 1e9))

        self.logger.info(
            "Row lookup finished | rows=%s first=%s",
            len(fetched),
            f"{fetched[0]['sheet_id']}:{fetched[0]['rowid']}" if fetched else None,
        )
        self.logger.debug("Row texts returned: %s", fetched)
        return {"row_texts": fetched} 