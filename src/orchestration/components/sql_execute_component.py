"""SQLExecuteComponent：执行 SQL 语句并返回结果 DataFrame/markdown。"""
from __future__ import annotations

from typing import Any, Dict, List

from sqlalchemy.exc import SQLAlchemyError  # type: ignore

from src.mcp.client import MC<PERSON>lient, QuerySQL, RenderDirect
from src.orchestration.base_component import BaseComponent, register_component


@register_component
class SQLExecuteComponent(BaseComponent):  # noqa: D101
    name = "sql_execute"

    inputs: List[str] = ["sql"]
    outputs: List[str] = ["sql_result"]

    # ------------------------------------------------------------------
    def setup(self, config: Dict[str, Any]) -> None:  # noqa: D401
        db_path = config.get("db_path", "output/data.db")
        self.limit: int | None = config.get("limit")
        self.render_style: str = config.get("render", "markdown")
        self.client = MCPClient(db_path)

    # ------------------------------------------------------------------
    def run(self, payload: Dict[str, Any], **kwargs: Any) -> Dict[str, Any]:  # noqa: D401
        sql_stmt: str = payload.get("sql", "").strip()
        if not sql_stmt:
            return {}

        try:
            df = self.client.query_sql(QuerySQL(sql=sql_stmt, limit=self.limit))
            txt = self.client.render_direct(RenderDirect(dataframe=df, style=self.render_style, max_rows=20))
        except SQLAlchemyError as exc:  # pragma: no cover
            print(f"[SQLExecuteComponent] Query failed: {exc}")
            txt = "(SQL 执行失败)"
        return {"sql_result": txt} 