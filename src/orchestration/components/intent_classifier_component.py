"""IntentClassifier 组件。"""
from __future__ import annotations

from typing import Dict, Any

from ..base_component import BaseComponent, register_component

from src.intent.classifier import IntentClassifier, IntentResult


@register_component
class IntentClassifierComponent(BaseComponent):  # noqa: D101
    name = "intent_classifier"
    inputs = ["query"]
    outputs = ["intent", "strategy", "strategy_params", "intent_result", "level_probs"]

    def setup(self, config: Dict[str, Any]) -> None:  # noqa: D401
        # 将流水线步骤中的配置传递给 IntentClassifier，避免依赖全局 app_settings.yaml
        self.cls = IntentClassifier(task_cfg=config or {})

    # ------------------------------------------------------------------
    def run(self, payload: Dict[str, Any], **kwargs: Any) -> Dict[str, Any]:  # noqa: D401
        question = payload.get("query", "")
        result: IntentResult = self.cls.classify(question)

        # ---------------- 向下游透传 min_prob 阈值 -------------------
        min_prob_cfg = self.config.get("min_prob")
        strategy_params = dict(result.params) if isinstance(result.params, dict) else {}
        if min_prob_cfg is not None:
            strategy_params["min_prob"] = float(min_prob_cfg)

        return {
            "intent": result.intent,
            "strategy": result.strategy,
            "strategy_params": strategy_params,
            "intent_result": result,
            "level_probs": result.level_probs,
        } 