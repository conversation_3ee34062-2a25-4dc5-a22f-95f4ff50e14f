"""DescribeTableComponent：获取指定表/全部表的字段结构与示例值。"""
from __future__ import annotations

from typing import Any, Dict, List

from sqlalchemy.exc import SQLAlchemyError  # type: ignore

from src.mcp.client import MCPClient, DescribeTable
from src.orchestration.base_component import BaseComponent, register_component


@register_component
class DescribeTableComponent(BaseComponent):  # noqa: D101
    name = "describe_table"

    inputs: List[str] = ["tables", "selected_table"]  # 若为空则描述全部
    outputs: List[str] = ["table_schemas"]

    # ------------------------------------------------------------------
    def setup(self, config: Dict[str, Any]) -> None:  # noqa: D401
        db_path = config.get("db_path", "output/data.db")
        self.sample_limit: int = config.get("sample_limit", 5)
        self.client = MCPClient(db_path)

    # ------------------------------------------------------------------
    def run(self, payload: Dict[str, Any], **kwargs: Any) -> Dict[str, Any]:  # noqa: D401
        sel: str | None = payload.get("selected_table")
        tables: List[str] = [sel] if sel else payload.get("tables", [])
        if not tables:
            # 若上一步未返回 tables，则列出全部表
            try:
                tables = self.client.list_tables()
            except SQLAlchemyError:
                tables = []

        schemas: Dict[str, Any] = {}
        for tbl in tables:
            try:
                dt = DescribeTable(table_name=tbl, sample_limit=self.sample_limit)
                schemas[tbl] = self.client.describe_table(dt)
            except SQLAlchemyError:
                continue
        return {"table_schemas": schemas} 