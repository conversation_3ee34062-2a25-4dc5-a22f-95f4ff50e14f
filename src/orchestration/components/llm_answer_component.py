"""调用大模型总结回答的组件。"""
from __future__ import annotations

from typing import Dict, Any, List
import json

from ..base_component import BaseComponent, register_component
from ..answer_llm import summarize_answer
from src.utils.config import get_task_cfg


@register_component
class LLMAnswerComponent(BaseComponent):  # noqa: D101
    name = "llm_answer"
    inputs = ["query", "tables_preview", "row_texts", "sheet_meta", "column_texts", "file_texts", "sql_result"]
    outputs = ["answer"]

    def setup(self, config: Dict[str, Any]) -> None:  # noqa: D401
        # 读取全局 YAML 设置并让流水线步骤配置覆盖全局
        qa_cfg = {**get_task_cfg("qa_answer"), **(config or {})}
        self.enable_summary: bool = qa_cfg.get("enable", True)

        # 是否启用"格式:"字段的专用抽取逻辑，默认关闭
        self.enable_format_extract: bool = qa_cfg.get("enable_format_extract", False)
        # 通用 system prompt（若未配置则回退到 summarize_answer 中的默认值）
        self.default_system_prompt: str | None = qa_cfg.get("system_prompt")

        # 是否让 LLM 决定是否展示预览
        self.llm_decide_preview: bool = qa_cfg.get("llm_decide_preview", False)

        # 保存 provider/model 供 run() 调用
        self._provider = qa_cfg.get("provider")
        self._model = qa_cfg.get("model")
        self._think = qa_cfg.get("think")
        import logging
        self.logger = logging.getLogger(self.name)

    # ------------------------------------------------------------------
    def run(self, payload: Dict[str, Any], **kwargs: Any) -> Dict[str, Any]:  # noqa: D401
        if not self.enable_summary:
            return {}
        question = payload.get("query", "")
        preview = payload.get("tables_preview", {})
        row_texts = payload.get("row_texts", [])
        file_texts = payload.get("file_texts", [])
        sql_result = payload.get("sql_result")
        sheet_meta = payload.get("sheet_meta", [])  # List[Dict]
        column_texts = payload.get("column_texts", [])  # List[Dict]

        # ---- 新增：根据 level_probs 动态调整上下文顺序 ----------------------
        level_probs: Dict[str, float] = payload.get("level_probs", {})  # type: ignore[assignment]
        if not level_probs:
            # 默认分布，与 VectorRecall 中保持一致
            level_probs = {"file": 0.1, "sheet": 0.1, "row": 0.8}

        # 只保留概率 >= 0.3 的层级，并按概率降序排列
        sorted_levels = [lvl for lvl, prob in sorted(level_probs.items(), key=lambda x: x[1], reverse=True) if prob >= 0.3]
        if not sorted_levels:
            # 若全部低于阈值，则回退全部层级（保持原顺序）
            sorted_levels = ["row", "sheet", "file"]

        # --- 组装各层级上下文 ------------------------------------------------
        ctx_by_level: Dict[str, List[str]] = {"file": [], "sheet": [], "row": []}

        # File 级文本
        for ft in file_texts:
            ctx = ft.get("text", "")
            if ctx:
                ctx_by_level["file"].append(f"[file {ft.get('file_id')}] {ctx}")

        # Sheet 级信息 (元数据 + 列文本 + 表预览)
        for meta in sheet_meta:
            desc = meta.get("description") or "(无描述)"
            ctx_by_level["sheet"].append(f"[sheet {meta.get('sheet_id')}] {desc}")

        for col in column_texts:
            col_ctx = col.get("text") or ""
            if col_ctx:
                ctx_by_level["sheet"].append(col_ctx)

        # 表预览（也归为 sheet 级）
        for name, rows in preview.items():
            sample_rows = rows[:3]
            ctx_by_level["sheet"].append(f"表 {name}: {sample_rows}")

        # Row 级文本
        if row_texts:
            row_snippets = [f"[sheet {r['sheet_id']} row {r['rowid']}] {r['text']}" for r in row_texts]
            ctx_by_level["row"].extend(row_snippets)

        # 按层级概率顺序拼接上下文
        context_parts: List[str] = []

        # 若有 SQL 查询结果，放在最前，便于直接回答
        if sql_result:
            context_parts.append(f"SQL 查询结果：\n{sql_result}")

        for lvl in sorted_levels:
            context_parts.extend(ctx_by_level.get(lvl, []))

        # 如果仍为空，则回退原流程
        if not context_parts:
            # 原先固定顺序构建，以免返回空上下文
            context_parts = ctx_by_level["sheet"] + ctx_by_level["row"] + ctx_by_level["file"]

        context = "\n".join(context_parts) or "(无上下文)"

        self.logger.debug(
            "LLM summarize start | question=%s preview_tables=%s row_texts=%s",
            question,
            len(preview),
            len(row_texts),
        )

        # ------------------------------------------------------------------
        # 1) 允许 LLM 直接返回结构化 JSON：{"answer": str, "show_preview": bool, "preview_tables": []}
        # 2) 向 ask() 传入 response_format，Ollama 会自动加上 format=json 字段 [[memory:3397493]].
        # 3) 解析失败则回退到 summarize_answer 的纯文本路径。
        # ------------------------------------------------------------------

        if self.llm_decide_preview:
            sys_prompt = (
                self.default_system_prompt
            )

            user_prompt = (
                f"用户问题：\n{question}\n\n"
                f"候选上下文：\n{context[:120*512]}\n\n"
            )

            from src.parser.llm.client import ask, LLMClientError

            try:
                resp = ask(
                    user_prompt,
                    system=sys_prompt,
                    provider=self._provider,
                    model=self._model,
                    think=self._think,
                    response_format={"type": "json_object"},
                )

                # ask() 可能已抽取 json block，但仍用 json.loads 解析
                data = json.loads(resp)

                # 兼容模型返回的中文字段名 "答案"
                ans_text = str(data.get("answer", "")).strip()
                if not ans_text and "答案" in data:
                    ans_text = str(data.get("答案", "")).strip()
                show_prev = bool(data.get("show_preview", False))
                out: Dict[str, Any] = {"answer": ans_text, "show_preview": show_prev}

                if "preview_tables" in data:
                    out["preview_tables"] = data["preview_tables"]

                # 如果不展示表格，则清空 tables_preview 以免上游误判
                if not show_prev:
                    out["tables_preview"] = {}

                return out
            except (LLMClientError, json.JSONDecodeError) as ex:
                self.logger.warning("LLM structured answer failed, fallback to plain summarize: %s", ex)

        # ------------------ Fallback：纯文本 summarize_answer ------------------
        answer = summarize_answer(question, context).strip()

        # -------------------- 后处理：移除表预览 --------------------------
        try:
            import re as _re

            def _filter_table_lines(text: str) -> str:  # noqa: D401
                """去除 markdown 或 rich 样式表格行，保留正常文本。"""
                filtered: list[str] = []
                for ln in text.splitlines():
                    # box drawing / rich 表格行
                    if _re.match(r"^\s*[┏┓┗┛┡┩┻┳┣┫┨┠╇╈╤╧╪─━│┃╭╮╯╰]", ln):
                        continue
                    # markdown 表格行（以 | 分隔并至少包含两个竖线）
                    if _re.match(r"^\s*\|.*\|\s*$", ln):
                        continue
                    filtered.append(ln)
                return "\n".join(filtered).strip()

            cleaned_answer = _filter_table_lines(answer)
            if cleaned_answer:
                answer = cleaned_answer
        except Exception as _ex:  # noqa: BLE001
            self.logger.debug("strip table preview failed: %s", _ex)

        self.logger.info("LLM summarize finished | answer_length=%s", len(answer))
        if answer:
            return {"answer": answer}
        return {} 