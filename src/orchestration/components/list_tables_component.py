"""ListTablesComponent：列出数据库所有业务表名。"""
from __future__ import annotations

from typing import Any, Dict, List

from sqlalchemy.exc import SQLAlchemyError  # type: ignore

from src.orchestration.base_component import BaseComponent, register_component
from src.mcp.client import MCPClient


@register_component
class ListTablesComponent(BaseComponent):  # noqa: D101
    name = "list_tables"

    inputs: List[str] = []
    outputs: List[str] = ["tables"]

    # ------------------------------------------------------------------
    def setup(self, config: Dict[str, Any]) -> None:  # noqa: D401
        db_path = config.get("db_path", "output/data.db")
        self.client = MCPClient(db_path)

    # ------------------------------------------------------------------
    def run(self, payload: Dict[str, Any], **kwargs: Any) -> Dict[str, Any]:  # noqa: D401
        try:
            tables = self.client.list_tables()
        except SQLAlchemyError as exc:  # pragma: no cover
            # 若数据库连接失败，返回空列表，不中断流程
            print(f"[ListTablesComponent] DB error: {exc}")
            tables = []
        return {"tables": tables} 