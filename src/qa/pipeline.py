from __future__ import annotations
from typing import List, Dict, Any
import sqlite3
import time

import pandas as pd  # type: ignore
from rich.console import Console
from rich.markdown import Markdown
from rich.logging import RichHandler
import logging

from rich.table import Table
# Replace Progress-based UI with Live
from rich.live import Live

from rich.prompt import Prompt

from ..utils.logger import setup_logging
from ..orchestration.dynamic_orchestrator import DynamicOrchestrator


"""QA Pipeline：在终端交互式展示问答全过程（向量召回 + MCP 查询）。"""


class QAPipeline:  # noqa: D101
    def __init__(self, db_path: str = "output/data.db", *, top_k_sheets: int = 3, top_n_rows: int = 5) -> None:  # noqa: D401
        self.console = Console()
        self.db_path = db_path
        self.top_k_sheets = top_k_sheets
        self.top_n_rows = top_n_rows
        # 日志写文件，同时在交互模式下也输出到 console 方便查看动作过程
        import logging
        # 调高日志级别到 DEBUG 以捕获更丰富信息
        setup_logging(level=logging.DEBUG)

        # 动态加一个 RichHandler 到 root logger（避免重复）
        root_logger = logging.getLogger()
        if not any(isinstance(h, RichHandler) for h in root_logger.handlers):
            rh = RichHandler(markup=True, rich_tracebacks=False)
            rh.setLevel(logging.INFO)
            root_logger.addHandler(rh)
        self.logger = logging.getLogger("qa.pipeline")
        # 指定 QA 流程专用配置文件
        self.orc = DynamicOrchestrator("config/qa_pipeline.yaml")

        # 启动时打印 MCP 能力看板
        self._display_capabilities()

    # ------------------------------------------------------------------
    def _display_capabilities(self) -> None:  # noqa: D401
        from ..mcp import MCPClient

        caps = MCPClient.list_capabilities()
        if not caps:
            return
        table = Table(title="MCP 支持能力", show_lines=True)
        table.add_column("action", style="cyan")
        table.add_column("说明", overflow="fold")
        for action, desc in caps.items():
            table.add_row(action, desc)
        self.console.print(table)

    def run_question(self, question: str) -> None:  # noqa: D401
        self.console.rule(f"🧠 [bold cyan]问题[/]: {question}")

        # ----------------------------------------------
        # Build pipeline to get component list & set up checklist UI
        # ----------------------------------------------
        self.orc.pipeline.build(question)
        components = self.orc.pipeline.components

        comp_names = [getattr(c, "name", c.__class__.__name__) for c in components]

        # 简易分组: 认为以下组件为上一步的子任务，可缩进显示
        SUBTASKS = {"sheet_meta", "mcp_query", "row_lookup", "column_lookup"}

        # 初始状态: 未开始 -> 灰色方框  □
        statuses: list[str] = ["[dim]□[/]"] * len(comp_names)
        timings: list[str] = [""] * len(comp_names)

        # 生成缩进前缀列表，与 comp_names 对齐
        indents: list[str] = [("  " if name in SUBTASKS else "") for name in comp_names]

        def _render_table() -> Table:  # noqa: D401
            table = Table(show_header=False, box=None)
            table.add_column("", width=3)
            table.add_column("Task")
            table.add_column("Time", justify="right", style="dim")
            for st, nm, indent, timing in zip(statuses, comp_names, indents, timings):
                table.add_row(st, f"{indent}{nm}", timing)
            return table

        # ---------------- Execute with Live UI ----------------
        payload: Dict[str, Any] = {"query": question}

        # Temporarily suppress console logs to prevent Live display corruption
        root_logger = logging.getLogger()
        rich_handler = next((h for h in root_logger.handlers if isinstance(h, RichHandler)), None)
        original_level = None
        if rich_handler:
            original_level = rich_handler.level
            rich_handler.setLevel(logging.WARNING)

        try:
            with Live(_render_table(), console=self.console, refresh_per_second=4, transient=True) as live:
                for idx, comp in enumerate(components):
                    # 标记为执行中 ▶
                    statuses[idx] = "[cyan]▶[/]"
                    live.update(_render_table())
                    comp_name = comp_names[idx]
                    start_time = time.monotonic()
                    try:
                        payload.update(comp.run(payload))
                    except Exception as exc:  # noqa: BLE001
                        # 失败时标记为 ✗
                        statuses[idx] = "[red]✗[/]"
                        self.logger.error("Component %s failed: %s", comp_name, exc, exc_info=exc)
                    else:
                        # 标记为完成 ✓
                        statuses[idx] = "[green]✓[/]"

                    # 更新计时并刷新
                    duration = time.monotonic() - start_time
                    timings[idx] = f"{duration:.2f}s"
                    live.update(_render_table())
        finally:
            # --- Restore console log level ---
            if rich_handler and original_level is not None:
                rich_handler.setLevel(original_level)

        # Live 已经渲染最终状态，避免重复输出

        # 若未生成 answer 字段，填充占位字符串，保持兼容上游逻辑
        answer = payload
        if "answer" not in answer:
            answer["answer"] = "(无答案)"
        # ---------------- Debug / Trace ----------------
        # 打印执行的组件顺序，便于定位是否调用了向量召回、MCP、LLM 等
        component_names = [getattr(c, "name", c.__class__.__name__) for c in self.orc.pipeline.components]
        self.console.print("[bold blue]执行组件顺序:[/] " + " → ".join(component_names))

        # 打印意图识别结果
        if intent := answer.get("intent"):
            self.console.print(f"[bold magenta]识别意图:[/] {intent}")
        if strategy := answer.get("strategy"):
            self.console.print(f"[bold magenta]策略:[/] {strategy} {answer.get('strategy_params', {})}")

        # 新增：打印 file/sheet/row 层级概率分布，便于调试
        level_probs_raw = answer.get("level_probs")
        if isinstance(level_probs_raw, dict) and level_probs_raw:
            # 格式化输出，例如 {file:0.2, sheet:0.5, row:0.3}
            formatted = ", ".join(f"{k}: {v:.2f}" for k, v in level_probs_raw.items())
            self.console.print(f"[bold magenta]层级概率分布:[/] {{{formatted}}}")

        # 打印向量召回结果（若有）
        if sheet_ids := answer.get("sheet_ids"):
            self.console.print(f"[bold magenta]向量召回 SheetIDs:[/] {sheet_ids}")

        # 新增：行级向量召回 & Rerank 结果 (Top5)
        def _print_rows(tag: str, rows: list[dict]):  # noqa: D401
            from rich.table import Table
            tbl = Table(title=tag, show_lines=True)
            tbl.add_column("sheet_id", style="cyan", justify="right")
            tbl.add_column("rowid", style="green", justify="right")
            tbl.add_column("text", overflow="fold")
            for r in rows[:5]:
                tbl.add_row(str(r.get("sheet_id")), str(r.get("rowid")), str(r.get("text", ""))[:120])
            self.console.print(tbl)

        if isinstance(answer.get("row_texts_raw"), list) and answer["row_texts_raw"]:
            _print_rows("行级召回 (Vector) Top5", answer["row_texts_raw"])

        if isinstance(answer.get("row_texts"), list) and answer["row_texts"]:
            _print_rows("行级召回 (Rerank) Top5", answer["row_texts"])

        # 如果调用了 MCP 返回了表预览，会在后续已有逻辑中展示
        if "tables_preview" not in answer:
            self.console.print("[bold yellow]未调用 MCP 或未查询到表数据")

        # 如果调用了 LLM 生成答案，可通过 answer 字段判断
        self.console.print("[bold magenta]LLM 是否参与:[/] " + ("是" if answer.get("answer") else "否"))
        # ------------------------------------------------

        self.console.rule("💡 [bold green]AI 回答")
        ans_text = answer.get("answer", "（无回答）")
        try:
            self.console.print(Markdown(ans_text))
        except Exception:  # noqa: BLE001
            # 若 Markdown 渲染失败，回退为普通文本
            self.console.print(ans_text)
        # ---------------- 表格预览展示策略 ----------------
        preview = answer.get("tables_preview")

        show_preview_flag: bool | None = answer.get("show_preview")  # LLM 可显式指示是否展示

        preview_tables_filter = answer.get("preview_tables")  # LLM 可指定要展示的表名列表

        def _should_show() -> bool:  # noqa: D401
            if show_preview_flag is not None:
                return bool(show_preview_flag)
            # 回退：若用户在问题里包含预览关键词
            preview_keywords = ["预览", "示例行", "preview"]
            return any(kw in question for kw in preview_keywords)

        if isinstance(preview, dict) and preview and _should_show():
            items = preview.items()
            if isinstance(preview_tables_filter, list) and preview_tables_filter:
                items = [(nm, rows) for nm, rows in preview.items() if nm in preview_tables_filter]

            for name, rows in items:
                self._display_table_preview(name, pd.DataFrame(rows))

    # ------------------------------------------------------------------
    # helpers
    # ------------------------------------------------------------------
    def _fetch_sheet_metadata(self, sheet_ids: List[str]):  # noqa: D401
        if not sheet_ids:
            return []
        ids_num = [int(s.split(":", 1)[1]) for s in sheet_ids]
        placeholders = ",".join("?" for _ in ids_num)
        conn = sqlite3.connect(self.db_path)
        cur = conn.execute(
            f"SELECT sheet_id, sheet_name, description FROM sheet_metadata WHERE sheet_id IN ({placeholders})",
            ids_num,
        )
        rows = cur.fetchall()
        conn.close()
        return rows

    def _display_sheets(self, sheets_info):  # noqa: D401
        table = Table(title="召回 Sheet", show_lines=True)
        table.add_column("sheet_id", style="cyan", justify="right")
        table.add_column("sheet_name", style="magenta")
        table.add_column("description", overflow="fold")
        for sid, name, desc in sheets_info:
            table.add_row(str(sid), str(name), str(desc))
        self.console.print(table)

    def _display_rows(self, sheet_id: int, df_rows: pd.DataFrame):  # noqa: D401
        table = Table(title=f"Sheet {sheet_id} 行级召回", show_lines=True)
        table.add_column("rowid", style="cyan", justify="right")
        table.add_column("text", overflow="fold")
        for _, row in df_rows.iterrows():
            table.add_row(str(row["rowid"]), str(row["text"]))
        self.console.print(table)

    def _display_table_preview(self, name: str, df: pd.DataFrame):  # noqa: D401
        # 1) 删除全空列，避免空白表
        df_clean = df.dropna(axis=1, how="all")

        # 若仍然全部为空，则不展示
        if df_clean.empty or df_clean.columns.empty:
            self.logger.debug("表 %s 预览为空或仅含空列，已跳过展示", name)
            return

        # 限制最大展示列数，防止终端宽度爆炸
        max_cols = 10
        if len(df_clean.columns) > max_cols:
            df_clean = df_clean.iloc[:, :max_cols]

        table = Table(title=f"表 {name} 示例行", show_lines=True)
        for col in df_clean.columns:
            table.add_column(str(col))
        for _, row in df_clean.iterrows():
            table.add_row(*[str(v) for v in row.values])
        self.console.print(table)

    # ------------------------------------------------------------------
    # Fallback helpers
    # ------------------------------------------------------------------
    def _extract_keywords(self, question: str) -> List[str]:  # noqa: D401
        import re

        tokens = re.findall(r"[\u4e00-\u9fa5A-Za-z0-9]+", question)
        # 过滤停用词可扩展，这里简单返回长度>1 的 token
        return [t for t in tokens if len(t) > 1]

    def _keyword_search_sheets(self, keywords: List[str]) -> List[str]:  # noqa: D401
        if not keywords:
            return []
        conn = sqlite3.connect(self.db_path)
        cur = conn.cursor()
        like_clauses = ["description LIKE ? OR sheet_name LIKE ?" for _ in keywords]
        sql = f"SELECT sheet_id FROM sheet_metadata WHERE {' OR '.join(like_clauses)} LIMIT ?"
        params: List[Any] = []
        for kw in keywords:
            like = f"%{kw}%"
            params.extend([like, like])
        params.append(self.top_k_sheets)
        cur.execute(sql, params)
        rows = cur.fetchall()
        conn.close()
        return [f"sheet:{r[0]}" for r in rows]


# ----------------------------------------------------------------------
# CLI 入口
# ----------------------------------------------------------------------

def main() -> None:  # noqa: D401
    console = Console()
    pipeline = QAPipeline()
    console.print("[bold green]Hierarchical Data QA Console[/]")
    while True:
        try:
            q = Prompt.ask("[bold yellow]请输入问题 (exit 退出) >[/]")
        except (EOFError, KeyboardInterrupt):
            console.print("\n再见！")
            break
        if q.lower() in {"exit", "quit", "q"}:
            break
        if not q.strip():
            continue
        pipeline.run_question(q)


if __name__ == "__main__":
    main() 