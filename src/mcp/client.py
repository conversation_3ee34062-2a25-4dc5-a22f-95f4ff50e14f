from __future__ import annotations
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, ClassVar

import pandas as pd  # type: ignore
from sqlalchemy import create_engine, text


"""MCP Client：提供统一的结构化数据查询接口。

当前仅支持 SQLite（由 DBWriter 写入的数据）。
未来可扩展至分布式数据库或 REST API，只需在同名方法中实现不同后端。
"""

__all__ = [
    "QueryTable",
    "RenderDirect",
    "RenderSummary",
    "ListTables",
    "DescribeTable",
    "QuerySQL",
    "MCPClient",
]

# ----------------------------------------------------------------------
# 能力清单（供控制台或 LLM Tool 看板展示）
# ----------------------------------------------------------------------


CAPABILITIES: Dict[str, str] = {
    "query_table": "查询单张表；支持 columns、filter、limit 等参数",
    "list_tables": "列出数据库中全部业务表名",
    "describe_table": "查询表结构（字段及示例值）",
    "query_sql": "执行任意 SQL 并返回结果 DataFrame",
    "render_direct": "将 DataFrame 直接渲染为 markdown/rich/json 等格式，不调用 LLM",
    "render_summary": "对 DataFrame 进行摘要/统计，调用轻量 summarizer 生成自然语言",
    # --- 新增实验性能力（均带有 Fallback 实现） ---------------------
    "lineage_trace": "字段/表血缘追溯，若无元数据则返回 fallback 消息",
    "mapping_dict_lookup": "查询码值/映射字典，若无数据则 fallback",
    "validation_rule_query": "查询字段/表校验规则，若无数据则 fallback",
    "business_glossary_lookup": "查询业务术语定义，若无数据则 fallback",
    "report_meta_query": "查询监管报表结构与元数据，若无数据则 fallback",
}

# ----------------------------------------------------------------------
# 查询 Schema（可序列化为 JSON，供 LLM 作为 Tool 参数）
# ----------------------------------------------------------------------


@dataclass
class QueryTable:  # noqa: D101
    """查询单张表。

    Parameters
    ----------
    table_name : str
        SQLite 中的物理表名。
    columns : list[str] | None, optional
        需返回的列；为空则返回全部列。
    filter : dict[str, Any] | None, optional
        形如 {"col": "= 'x'"} 或 {"col": "> 3"} 的简易表达式；
        复杂过滤可由调用方直接拼接 SQL。
    limit : int | None, optional
        最大返回行数。
    """

    table_name: str
    columns: Optional[List[str]] = None
    filter: Optional[Dict[str, str]] = None
    limit: Optional[int] = None

    # action 字段供 LLM Tool 标识
    action: str = "query_table"

    def to_dict(self) -> Dict[str, Any]:  # noqa: D401
        """序列化为 JSON 兼容字典。"""
        return {
            "action": self.action,
            "table_name": self.table_name,
            "columns": self.columns,
            "filter": self.filter,
            "limit": self.limit,
        }


# ----------------------------------------------------------------------
# Render Schema
# ----------------------------------------------------------------------


@dataclass
class RenderDirect:  # noqa: D101
    """直接渲染 DataFrame。

    Parameters
    ----------
    dataframe : pandas.DataFrame
        要渲染的 DataFrame 对象（仅限进程内调用时传对象）。
    style : str, optional
        输出格式：markdown / json / rich；默认 markdown。
    max_rows : int, optional
        预览的最大行数，默认 10。
    """

    dataframe: "pd.DataFrame"
    style: str = "markdown"
    max_rows: int = 10

    action: str = "render_direct"


@dataclass
class RenderSummary:  # noqa: D101
    """对 DataFrame 做轻量摘要（非 LLM）。"""

    dataframe: "pd.DataFrame"
    table_name: str = "table"
    max_cols: int = 5

    action: str = "render_summary"


# ----------------------------------------------------------------------
# 新增查询 Schema
# ----------------------------------------------------------------------


@dataclass
class ListTables:  # noqa: D101
    """列出数据库全部表名。"""

    action: str = "list_tables"

    def to_dict(self) -> Dict[str, Any]:  # noqa: D401
        return {"action": self.action}


@dataclass
class DescribeTable:  # noqa: D101
    """查询表结构及示例值。"""

    table_name: str
    sample_limit: int = 5

    action: str = "describe_table"

    def to_dict(self) -> Dict[str, Any]:  # noqa: D401
        return {
            "action": self.action,
            "table_name": self.table_name,
            "sample_limit": self.sample_limit,
        }


@dataclass
class QuerySQL:  # noqa: D101
    """执行任意 SQL 查询。"""

    sql: str
    limit: int | None = None  # 如果提供，将在 SQL 外层加 limit

    action: str = "query_sql"

    def to_dict(self) -> Dict[str, Any]:  # noqa: D401
        return {"action": self.action, "sql": self.sql, "limit": self.limit}


# ----------------------------------------------------------------------
# MCP Client 实现
# ----------------------------------------------------------------------


class MCPClient:  # noqa: D101
    def __init__(self, db_path: str | None = "output/data.db") -> None:  # noqa: D401
        self.engine = create_engine(f"sqlite:///{db_path}")

    # ------------------------------------------------------------------
    # Capabilities helper
    # ------------------------------------------------------------------

    CAPABILITIES: ClassVar[Dict[str, str]] = CAPABILITIES

    @classmethod
    def list_capabilities(cls) -> Dict[str, str]:  # noqa: D401
        """返回 action->description 的映射。"""
        return cls.CAPABILITIES

    # ---- 高层接口 ---------------------------------------------------
    def execute(self, payload: Dict[str, Any]) -> Any:  # noqa: D401
        """接收 dict/json 形式的 Tool 调用参数，转发至相应方法。"""
        action = payload.get("action")
        if action == "query_table":
            qt = QueryTable(
                table_name=payload["table_name"],
                columns=payload.get("columns"),
                filter=payload.get("filter"),
                limit=payload.get("limit"),
            )
            return self.query_table(qt)
        elif action == "list_tables":
            return self.list_tables()
        elif action == "describe_table":
            dt = DescribeTable(
                table_name=payload["table_name"],
                sample_limit=payload.get("sample_limit", 5),
            )
            return self.describe_table(dt)
        elif action == "query_sql":
            qs = QuerySQL(sql=payload["sql"], limit=payload.get("limit"))
            return self.query_sql(qs)
        elif action == "render_direct":
            rd = RenderDirect(
                dataframe=payload["dataframe"],
                style=payload.get("style", "markdown"),
                max_rows=payload.get("max_rows", 10),
            )
            return self.render_direct(rd)
        elif action == "render_summary":
            rs = RenderSummary(
                dataframe=payload["dataframe"],
                table_name=payload.get("table_name", "table"),
                max_cols=payload.get("max_cols", 5),
            )
            return self.render_summary(rs)
        # ---------------- 新增能力 ------------------------------------
        elif action == "lineage_trace":
            return self.lineage_trace(payload.get("table_name"), payload.get("column"))
        elif action == "mapping_dict_lookup":
            return self.mapping_dict_lookup(payload.get("code_set"), payload.get("column"))
        elif action == "validation_rule_query":
            return self.validation_rule_query(payload.get("table_name"))
        elif action == "business_glossary_lookup":
            return self.business_glossary_lookup(payload.get("term"))
        elif action == "report_meta_query":
            return self.report_meta_query(payload.get("report"), payload.get("section"))
        raise ValueError(f"Unsupported action: {action}")

    # ---- 具体查询 ---------------------------------------------------
    def query_table(self, qt: QueryTable) -> pd.DataFrame:  # noqa: D401
        """执行 Table 查询并返回 pandas.DataFrame。"""
        cols_sql = ", ".join(qt.columns) if qt.columns else "*"
        sql = f"SELECT {cols_sql} FROM \"{qt.table_name}\""

        if qt.filter:
            conditions = [f"\"{col}\" {expr}" for col, expr in qt.filter.items()]
            sql += " WHERE " + " AND ".join(conditions)

        if qt.limit:
            sql += f" LIMIT {qt.limit}"

        with self.engine.begin() as conn:
            df = pd.read_sql(text(sql), conn)
        return df 

    # ------------------------------------------------------------------
    # 新增能力实现
    # ------------------------------------------------------------------

    def list_tables(self) -> List[str]:  # noqa: D401
        """返回数据库中所有业务表名（排除 SQLite 内部表）。"""
        sql = "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%' ORDER BY name"
        with self.engine.begin() as conn:
            rows = conn.execute(text(sql)).fetchall()
        return [str(r[0]) for r in rows]

    def describe_table(self, dt: DescribeTable) -> Dict[str, Any]:  # noqa: D401
        """返回表结构：字段名、类型及示例值。"""
        table = dt.table_name
        sample_limit = dt.sample_limit
        with self.engine.begin() as conn:
            # 获取列及类型
            pragma_sql = f"PRAGMA table_info(\"{table}\")"
            cols_info = conn.execute(text(pragma_sql)).fetchall()
            columns: List[Dict[str, Any]] = []
            if not cols_info:
                return {"table_name": table, "columns": []}

            # 取样数据
            sample_sql = f"SELECT * FROM \"{table}\" LIMIT {sample_limit}"
            df_sample = pd.read_sql(text(sample_sql), conn)

            for col in cols_info:
                name = str(col[1])  # col[1] 是 name
                col_type = str(col[2])  # col[2] 是 type
                # 获取非空唯一示例值
                samples: List[Any] = []
                if name in df_sample.columns:
                    values = df_sample[name].dropna().unique().tolist()
                    samples = values[:sample_limit]
                columns.append({"name": name, "type": col_type, "sample_values": samples})
        return {"table_name": table, "columns": columns}

    def query_sql(self, qs: QuerySQL) -> pd.DataFrame:  # noqa: D401
        """执行任意 SQL，并返回 DataFrame。"""
        sql = qs.sql.strip().rstrip(";")
        if qs.limit is not None and " limit " not in sql.lower():
            sql += f" LIMIT {qs.limit}"
        with self.engine.begin() as conn:
            df = pd.read_sql(text(sql), conn)
        return df

    # ------------------------------------------------------------------
    # Fallback 实现的新能力
    # ------------------------------------------------------------------

    @staticmethod
    def _fallback_msg(feature: str) -> str:  # noqa: D401
        return f"【Fallback】{feature} 暂未配置元数据，已回退到向量检索或请人工确认。"

    def lineage_trace(self, table_name: str | None, column: str | None = None) -> str:  # noqa: D401
        if not table_name:
            return self._fallback_msg("血缘追溯 (table_name 缺失)")
        # TODO: 若未来有 bloodline 表，可在此查询
        return self._fallback_msg("血缘追溯")

    def mapping_dict_lookup(self, code_set: str | None, column: str | None = None) -> str:  # noqa: D401
        if not (code_set or column):
            return self._fallback_msg("码值映射 (参数缺失)")
        return self._fallback_msg("码值映射")

    def validation_rule_query(self, table_name: str | None) -> str:  # noqa: D401
        if not table_name:
            return self._fallback_msg("校验规则 (table_name 缺失)")
        return self._fallback_msg("校验规则")

    def business_glossary_lookup(self, term: str | None) -> str:  # noqa: D401
        if not term:
            return self._fallback_msg("业务术语 (term 缺失)")
        return self._fallback_msg("业务术语定义")

    def report_meta_query(self, report: str | None, section: str | None = None) -> str:  # noqa: D401
        if not report:
            return self._fallback_msg("报表元数据 (report 缺失)")
        return self._fallback_msg("报表元数据")

    # ------------------------------------------------------------------
    # Render helpers
    # ------------------------------------------------------------------

    @staticmethod
    def render_direct(rd: RenderDirect) -> str:  # noqa: D401
        """根据 style 将 DataFrame 渲染成文本。"""
        df = rd.dataframe.head(rd.max_rows)
        if rd.style == "json":
            return df.to_json(orient="records", force_ascii=False) or ""
        if rd.style == "rich":
            try:
                from rich.table import Table
                tbl = Table(title="Data Preview")
                for col in df.columns:
                    tbl.add_column(str(col))
                for _, row in df.iterrows():
                    tbl.add_row(*[str(v) for v in row.values])
                from rich.console import Console

                console = Console(record=True)
                console.print(tbl)
                return console.export_text() or ""
            except Exception:  # noqa: BLE001
                # 回退 markdown
                rd.style = "markdown"
        # 默认 markdown
        return df.to_markdown(index=False) or ""

    @staticmethod
    def render_summary(rs: RenderSummary) -> str:  # noqa: D401
        """用固定模板对表格做摘要，不调用 LLM。"""
        try:
            from ..ingestion.summarizer import summarize_table  # type: ignore
        except Exception:
            def summarize_table(name, df, max_cols=5):  # type: ignore
                cols = list(df.columns)[:max_cols]
                return f"表格 {name}，行数 {len(df)}，列示例：{', '.join(map(str, cols))}。"

        return summarize_table(rs.table_name, rs.dataframe, max_cols=rs.max_cols) 