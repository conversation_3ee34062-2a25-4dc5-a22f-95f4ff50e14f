from __future__ import annotations

"""统一日志工具。"""

import logging
import sys
from logging.handlers import RotatingFileHandler
from pathlib import Path


def setup_logging(log_file: str = "logs/qa_pipeline.log", level: int = logging.INFO) -> None:  # noqa: D401
    Path(log_file).parent.mkdir(parents=True, exist_ok=True)

    fmt = "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
    handler = RotatingFileHandler(
        log_file, 
        maxBytes=5 * 1024 * 1024, 
        backupCount=3, 
        encoding="utf-8"
    )
    # 设置为不缓冲日志，确保立即写入文件
    handler.setFormatter(logging.Formatter(fmt))
    
    # 设置立即刷新日志文件
    handler.setLevel(level)

    root = logging.getLogger()
    root.setLevel(level)

    # Suppress extremely verbose logs from markdown_it rules_block parser
    logging.getLogger("markdown_it.rules_block").setLevel(logging.WARNING)
    
    # 避免重复添加 handler
    if not any(isinstance(h, RotatingFileHandler) and h.baseFilename == Path(log_file) for h in root.handlers):
        root.addHandler(handler)
        
    # 配置Python的stdout和stderr不缓冲
    sys.stdout.reconfigure(line_buffering=True)  # type: ignore
    sys.stderr.reconfigure(line_buffering=True)  # type: ignore 