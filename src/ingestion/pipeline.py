from __future__ import annotations

"""
Pipeline 模块：串联 ExcelParser、DBWriter、Vectorizer、VectorStore。
最终提供 CLI 调用接口。详细流程参见 docs/excel_ingestion_design.md。
"""

from pathlib import Path
from typing import List, Sequence, Optional, Any, Dict
import sys

import pandas as pd  # type: ignore
from sqlalchemy import text
from ..storage.db import DBWriter  # type: ignore  # 延迟解决循环引用
from ..vectorization.vectorizer import Vectorizer  # type: ignore
from ..vectorization.vector_store import ChromaVectorStore  # type: ignore
from .summarizer import summarize_sheet, summarize_file, summarize_table
from .excel_parser import ExcelParser, ParsedTable
import logging

logger = logging.getLogger(__name__)

# 确保日志文件句柄刷新函数
def flush_logs() -> None:
    """强制刷新所有日志处理器的缓冲区"""
    root = logging.getLogger()
    for handler in root.handlers:
        handler.flush()


class Pipeline:  # noqa: D101
    def __init__(
        self,
        root_dir: Path | str = "data_files",
        db_path: Path | str = "output/data.db",
        *,
        vectorizer: Optional[Vectorizer] = None,
        vector_store: Optional[ChromaVectorStore] = None,
    ) -> None:
        self.parser = ExcelParser(root_dir)
        self.db_writer = DBWriter(db_path)  # noqa: E501
        self.vectorizer = vectorizer if vectorizer is not None else Vectorizer()
        self.vector_store = vector_store if vector_store is not None else ChromaVectorStore()

        # sheet progress tracking
        self._sheet_progress: Optional[Any] = None  # Progress instance
        self._sheet_task_id: Optional[int] = None

    # high-level API
    def run(self, excel_files: Sequence[Path | str] | None = None, *, overwrite: bool = False) -> None:  # noqa: D401
        """主入口：对指定文件集合（或 root_dir 全量）执行解析—存储—向量化。"""

        # --------------------------------------------------------------
        # 断点续传准备：清理孤立记录，仅执行一次
        # --------------------------------------------------------------
        if not hasattr(self, "_cleanup_done"):
            try:
                self.db_writer.cleanup_orphan_records()
            except Exception as exc:  # noqa: BLE001
                logger.warning("数据库孤立记录清理失败: %s", exc)
            setattr(self, "_cleanup_done", True)

        # --------------------------------------------------------------
        # 构造待处理文件列表，并过滤已完成的文件
        # --------------------------------------------------------------
        files = [Path(p) for p in (excel_files or self.parser.discover_excels())]

        # 跳过已在 file_metadata 中且 status='done' 的文件
        pending_files: list[Path] = []
        for fp in files:
            info = self.db_writer.get_file_info(str(fp))
            if info is not None:
                file_id_curr, status_curr = info
                if status_curr == "done":
                    logger.info("跳过已完成文件: %s (file_id=%s)", fp.name, file_id_curr)
                    continue
            pending_files.append(fp)

        # 若全部已处理完毕则直接返回
        if not pending_files:
            logger.info("所有文件均已在数据库中，无需处理。")
            flush_logs()
            return

        # --------------------------------------------------------------
        # 正式处理未完成的文件
        # --------------------------------------------------------------
        for excel_path in pending_files:
            try:
                raw_sheet_cnt = len(pd.ExcelFile(excel_path, engine="openpyxl").sheet_names)  # type: ignore[arg-type]
            except Exception:
                raw_sheet_cnt = 0  # Fallback

            parsed_tables_generator = self.parser.parse_excel(excel_path)

            sheet_summaries: List[str] = []
            file_id: Optional[int] = None
            processed_sheets_count = 0
            
            logger.info("开始处理文件: %s", excel_path)
            flush_logs()

            # ---- Sheet 级处理 -------------------------------------------
            for parsed in parsed_tables_generator:
                processed_sheets_count += 1

                # 在处理第一个有效 sheet 时，创建文件元数据记录
                if file_id is None:
                    file_id = self.db_writer.upsert_file_metadata(
                        str(excel_path), attributes={}, vectors={}, embedding_texts={}, status="pending"
                    )
                    logger.info("创建文件元数据记录, file_id=%s", file_id)
                    flush_logs()

                # 在生成 Sheet 摘要前，更新进度条提示
                if self._sheet_progress is not None and self._sheet_task_id is not None:
                    sheet_label = str(parsed.meta.get("sheet_name", parsed.name))[:10]
                    try:
                        self._sheet_progress.update(
                            self._sheet_task_id, description=f"[yellow]LLM 摘要 {sheet_label}"  # noqa: WPS336
                        )
                    except Exception:  # noqa: BLE001
                        pass

                # ------------- 生成 Sheet 摘要 -----------------------------
                sheet_name = str(parsed.meta.get("sheet_name", parsed.name))
                sheet_desc = summarize_sheet(sheet_name, parsed.dataframe)
                desc_text = str(parsed.meta.get("description_text", "")).strip()
                combined_desc = f"{desc_text}\n{sheet_desc}" if desc_text else sheet_desc
                sheet_summaries.append(sheet_desc)  # 为文件摘要收集

                # ---------- 构造包含表名的 embedding 文本 ------------------
                embedding_desc = f"数据表名称：{sheet_name} ， {combined_desc}"

                # Sheet 级属性及向量
                sheet_attributes = {"description": combined_desc}
                sheet_embedding_texts = {"description": embedding_desc}
                sheet_vectors = {}
                sheet_desc_vector_list = self.vectorizer.embed_texts([embedding_desc])
                sheet_desc_vector = sheet_desc_vector_list[0] if sheet_desc_vector_list else []
                if sheet_desc_vector:
                    sheet_vectors["description"] = sheet_desc_vector

                # upsert sheet metadata
                sheet_id = self.db_writer.upsert_sheet_metadata(
                    file_id=file_id,
                    sheet_name=parsed.meta.get("sheet_name", parsed.name),
                    table_name=parsed.name,
                    attributes=sheet_attributes,
                    vectors=sheet_vectors,
                    embedding_texts=sheet_embedding_texts,
                    status="pending",
                )

                # ---- 向量写入：Sheet 级 -----------------------------------
                if sheet_desc_vector:
                    try:
                        self.vector_store.add_embeddings(
                            ids=[f"sheet:{sheet_id}"],
                            embeddings=[sheet_desc_vector],
                            metadata=[
                                {"sheet_id": sheet_id, "file_id": file_id, "level": "sheet", "key": "description"}
                            ],
                        )  # type: ignore[arg-type]
                    except Exception:
                        pass

                # 若存在原始描述文本，额外写入一条独立向量
                if desc_text:
                    desc_vec_list = self.vectorizer.embed_texts([desc_text])
                    desc_vec = desc_vec_list[0] if desc_vec_list else []
                    if desc_vec:
                        try:
                            self.vector_store.add_embeddings(
                                ids=[f"sheet_desc:{sheet_id}"],
                                embeddings=[desc_vec],
                                metadata=[
                                    {
                                        "sheet_id": sheet_id,
                                        "file_id": file_id,
                                        "level": "sheet_desc",
                                        "key": "original_description",
                                    }
                                ],
                            )  # type: ignore[arg-type]
                            self.db_writer.insert_sheet_attributes(
                                sheet_id,
                                {"original_description": desc_text},
                                vectors={"original_description": desc_vec},
                                embedding_texts={"original_description": desc_text},
                            )
                        except Exception:  # noqa: BLE001
                            pass

                self._process_parsed_table(parsed, file_id=file_id, sheet_id=sheet_id, overwrite=overwrite)
                self.db_writer.set_sheet_status(sheet_id, "done")
                
                logger.info("Sheet处理完成: %s", parsed.meta.get("sheet_name", parsed.name))
                flush_logs()

            # ---- 文件级摘要与状态更新 -----------------------------------
            if file_id is not None:
                if sheet_summaries:
                    # 生成文件摘要前提示
                    if self._sheet_progress is not None and self._sheet_task_id is not None:
                        try:
                            self._sheet_progress.update(
                                self._sheet_task_id,
                                description=f"[yellow]LLM 文件摘要 {excel_path.name[:10]}",  # noqa: WPS336
                            )
                        except Exception:  # noqa: BLE001
                            pass

                    file_desc = summarize_file(excel_path.name, sheet_summaries)
                    file_desc_vector_list = self.vectorizer.embed_texts([file_desc])
                    file_desc_vector = file_desc_vector_list[0] if file_desc_vector_list else []

                    # 文件级属性及向量
                    file_attributes = {"description": file_desc}
                    file_embedding_texts = {"description": file_desc}
                    file_vectors = {}
                    if file_desc_vector:
                        file_vectors["description"] = file_desc_vector

                    # 更新文件元数据，写入摘要信息
                    self.db_writer.upsert_file_metadata(
                        str(excel_path),
                        attributes=file_attributes,
                        vectors=file_vectors,
                        embedding_texts=file_embedding_texts,
                        status="pending",  # 状态稍后统一更新为 done
                    )

                    # 向量写入：文件级
                    if file_desc_vector:
                        try:
                            self.vector_store.add_embeddings(
                                ids=[f"file:{file_id}:description"],
                                embeddings=[file_desc_vector],
                                metadata=[{"file_id": file_id, "level": "file", "key": "description"}],
                            )  # type: ignore[arg-type]
                        except Exception:
                            pass

                self.db_writer.set_file_status(file_id, "done")
                logger.info("文件处理完成: %s (file_id=%s)", excel_path.name, file_id)
                flush_logs()

            # 更新空白 sheet 的进度
            skipped_sheets = raw_sheet_cnt - processed_sheets_count
            if skipped_sheets > 0 and self._sheet_progress is not None and self._sheet_task_id is not None:
                try:
                    self._sheet_progress.advance(self._sheet_task_id, advance=skipped_sheets)
                except Exception:
                    pass

    # ------------------------------------------------------------------
    # Internal helpers

    def attach_sheet_progress(self, progress: Any, task_id: int) -> None:  # noqa: D401
        """Attach Rich Progress for per-sheet advancement."""
        self._sheet_progress = progress
        self._sheet_task_id = task_id

    def _process_parsed_table(self, parsed: ParsedTable, *, file_id: int, sheet_id: int, overwrite: bool) -> None:  # noqa: D401
        table_name = self.db_writer.write_table(parsed, if_exists="replace" if overwrite else "append")

        # ---- 表格块级摘要向量 -----------------------------------------
        table_desc = summarize_table(table_name, parsed.dataframe)
        table_vec_list = self.vectorizer.embed_texts([table_desc])
        table_vec = table_vec_list[0] if table_vec_list else []
        if table_vec:
            try:
                self.vector_store.add_embeddings(
                    ids=[f"table:{table_name}"],
                    embeddings=[table_vec],
                    metadata=[{
                        "table_name": table_name,
                        "sheet_id": sheet_id,
                        "file_id": file_id,
                        "level": "table",
                        "key": "description",
                    }],
                )  # type: ignore[arg-type]
                
                # 添加到 sheet 属性
                self.db_writer.insert_sheet_attributes(
                    sheet_id, 
                    {"table_description": table_desc},
                    vectors={"table_description": table_vec},
                    embedding_texts={"table_description": table_desc}
                )
            except Exception:  # noqa: BLE001
                pass

        # Update progress bar description
        if self._sheet_progress is not None and self._sheet_task_id is not None:
            sheet_label = str(parsed.meta.get("sheet_name", parsed.name))[:10]
            self._sheet_progress.update(self._sheet_task_id, description=f"[blue]{sheet_label}")

        # ---- 行级向量化 -------------------------------------------------
        # Step 0: Detect constant large-text columns and remove them from row vectorization
        CONST_LEN_THRESHOLD = 30  # characters; adjustable via config later if needed

        constant_cols: Dict[str, str] = {}
        for col in parsed.dataframe.columns:
            series = parsed.dataframe[col]
            # consider value count including nulls; nunique(dropna=False)==1 means entire column same (or all NaN)
            if series.nunique(dropna=False) != 1:
                continue

            # Extract the unique value (could be NaN)
            unique_val_series = series.dropna().unique()
            if len(unique_val_series) == 0:
                continue
            unique_val = unique_val_series[0]

            text_val = str(unique_val).strip()
            if len(text_val) < CONST_LEN_THRESHOLD:
                continue

            constant_cols[col] = text_val

        # If constant columns detected, store them as separate vectors once per table
        if constant_cols:
            const_texts = [f"{name}: {txt}" for name, txt in constant_cols.items()]
            const_embeddings = self.vectorizer.embed_texts(const_texts)
            
            # 将常量列添加到 sheet 属性
            column_attributes = {}
            column_vectors = {}
            column_embedding_texts = {}
            
            for i, (col_name, col_text_val) in enumerate(constant_cols.items()):
                key = f"column:{col_name}"
                column_attributes[key] = col_text_val
                column_embedding_texts[key] = const_texts[i]
                if i < len(const_embeddings) and const_embeddings[i]:
                    column_vectors[key] = const_embeddings[i]
            
            # 添加到 sheet 属性
            if column_attributes:
                self.db_writer.insert_sheet_attributes(
                    sheet_id,
                    column_attributes,
                    vectors=column_vectors,
                    embedding_texts=column_embedding_texts
                )

            const_ids = [f"const:{table_name}:{name}" for name in constant_cols.keys()]
            const_metas = [
                {
                    "table_name": table_name,
                    "sheet_id": sheet_id,
                    "file_id": file_id,
                    "level": "column_constant",
                    "column_name": name,
                }
                for name in constant_cols.keys()
            ]

            valid_embeddings = [e for e in const_embeddings if e]
            if len(valid_embeddings) > 0:
                valid_ids = [const_ids[i] for i, e in enumerate(const_embeddings) if e]
                valid_metas = [const_metas[i] for i, e in enumerate(const_embeddings) if e]
                if len(valid_ids) > 0:
                    try:
                        self.vector_store.add_embeddings(valid_ids, valid_embeddings, valid_metas)  # type: ignore[arg-type]
                    except Exception as ex:  # noqa: BLE001
                        logger.error("VectorStore add (constant columns) failed: %s", ex)

        # 继续原有代码 - 选择文本列
        columns = self._select_text_columns(parsed.dataframe)
        # 从文本列中排除常量列，避免重复向量化
        filtered_columns = [col for col in columns if col not in constant_cols]

        # ---- 行级文本 & 向量 ------------------------------------------
        if len(filtered_columns) > 0:
            # 生成行文本（无论是否启用向量化都需要，以便后续补写向量）
            rows_as_text: list[str] = []
            for _, row in parsed.dataframe.iterrows():
                row_text_content = self._serialize_row_text(row, filtered_columns)
                if row_text_content.strip():
                    rows_as_text.append(row_text_content)

            if rows_as_text:
                # 若 Vectorizer 被禁用（DummyVectorizer），embed_texts 将返回 [] 列表；
                # 我们仍然调用一次以保持长度一致，后续 _insert_row_vectors 会处理空向量情况。
                embeddings = self._embed_in_batches(rows_as_text)
                self._insert_row_vectors(
                    table_name,
                    file_id,
                    sheet_id,
                    embeddings,
                    rows_as_text,
                )

        # advance sheet progress
        if self._sheet_progress is not None and self._sheet_task_id is not None:
            try:
                self._sheet_progress.advance(self._sheet_task_id)
            except Exception:
                pass

    # 以下方法保持不变
    @staticmethod
    def _select_text_columns(df: pd.DataFrame) -> List[str]:  # noqa: D401
        """选取适合向量化的文本列。"""
        columns = []
        for col in df.columns:
            # 数值列不参与向量化
            if pd.api.types.is_numeric_dtype(df[col]):
                continue
            # 空列不参与向量化
            if bool(df[col].isna().all()):
                continue
            # 仅含短文本的列（每行平均长度<3）不参与
            try:
                # The result of mean() on an empty series is NaN, which is a float.
                avg_len = df[col].astype(str).str.len().mean()
                if not pd.isna(avg_len) and avg_len < 3:
                    continue
            except (ValueError, TypeError):
                continue
            columns.append(col)
        return columns

    @staticmethod
    def _serialize_row_text(row: pd.Series, columns: List[str]) -> str:  # noqa: D401
        """将行数据序列化为文本。"""
        parts = []
        for col in columns:
            val = row.get(col)
            if bool(pd.isna(val)):
                continue
            parts.append(f"{col}: {val}")
        return " | ".join(parts)

    def _insert_row_vectors(
        self,
        table_name: str,
        file_id: int,
        sheet_id: int,
        embeddings: List[List[float]],
        texts: List[str],
    ) -> None:  # noqa: D401
        """批量插入 row_vectors 记录与向量库。"""
        # SQLite 批量插入
        records = []
        valid_indices = []  # Track which embeddings are valid for VectorStore

        for i, text_val in enumerate(texts):
            # 若 embeddings 列表长度不足，则视为无向量
            emb: list[float] | None = embeddings[i] if i < len(embeddings) else None

            blob = None
            if emb is not None and len(emb) > 0:
                try:
                    blob = self.db_writer._vector_to_blob(emb)  # type: ignore[attr-defined]
                except Exception:
                    blob = None

            # 在文本前添加表名作为前缀
            prefixed_text = f"表名:{table_name} | {text_val}"

            records.append(
                {
                    "sheet_id": sheet_id,
                    "rowid": i,  # 行号从 0 开始
                    "file_id": file_id,
                    "table_name": table_name,
                    "vector": blob,
                    "text": prefixed_text,
                }
            )

            if blob is not None:
                valid_indices.append(i)

        # 批量写入 SQLite（始终写入行文本，vector 允许为空）
        if not records:
            return

        with self.db_writer.engine.begin() as conn:
            conn.execute(
                text(
                    """
INSERT OR REPLACE INTO row_vectors (sheet_id, rowid, file_id, table_name, vector, text)
VALUES (:sheet_id, :rowid, :file_id, :table_name, :vector, :text)
"""
                ),
                records,
            )

        # 批量写入 VectorStore
        try:
            # 生成唯一标识符
            row_ids = [f"{sheet_id}:{rec['rowid']}" for rec in records]
            # 仅提取有效的 embeddings（排除空向量）
            valid_embeddings = [embeddings[i] for i in valid_indices if i < len(embeddings) and embeddings[i]]
            # 构建元数据
            valid_metas = [
                {
                    "sheet_id": sheet_id,
                    "file_id": file_id,
                    "table_name": table_name,
                    "rowid": rec["rowid"],
                    "level": "row",
                }
                for rec in records
            ]

            if row_ids and valid_embeddings and len(row_ids) == len(valid_embeddings) and len(valid_embeddings) == len(valid_metas):
                self.vector_store.add_embeddings(row_ids, valid_embeddings, valid_metas)  # type: ignore[arg-type]
        except Exception as ex:  # noqa: BLE001
            logger.error("VectorStore add (rows) failed: %s", ex)

    def _embed_in_batches(self, texts: list[str], *, batch_size: int = 64) -> list[list[float]]:  # noqa: D401
        """分批次向量化，避免内存爆炸。"""
        all_embeddings: list[list[float]] = []
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i : i + batch_size]
            batch_embeddings = self.vectorizer.embed_texts(batch_texts)
            all_embeddings.extend(batch_embeddings)
        return all_embeddings 