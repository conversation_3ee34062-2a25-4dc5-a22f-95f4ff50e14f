#!/usr/bin/env python3
"""
测试表名匹配增强逻辑
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.vectorization.vectorizer import embed_texts
from src.vectorization.vector_store import ChromaVectorStore
import re
from difflib import SequenceMatcher

def extract_table_keywords(query: str) -> list[str]:
    """从查询中提取可能的表名关键词"""
    # 常见的表名模式
    table_patterns = [
        r"(\w*表)",  # 以"表"结尾的词
        r"(\w*信息表)",  # 以"信息表"结尾的词
        r"(\w*业务\w*表)",  # 包含"业务"的表名
        r"(\w*余额表)",  # 以"余额表"结尾的词
        r"(\w*明细表)",  # 以"明细表"结尾的词
    ]
    
    keywords = []
    for pattern in table_patterns:
        matches = re.findall(pattern, query)
        keywords.extend(matches)
    
    # 去重并过滤掉过短的关键词
    keywords = list(set([kw for kw in keywords if len(kw) >= 3]))
    return keywords

def calculate_enhanced_score(text: str, query: str, base_distance: float) -> float:
    """计算增强后的得分"""
    # 基础向量得分
    vec_score = 1.0 / (1.0 + base_distance)
    
    # 关键词相似度
    q_norm = re.sub(r"[\s,:|]", "", query)
    text_prefix = text[:len(q_norm) + 20]
    text_prefix_norm = re.sub(r"[\s,:|]", "", text_prefix)
    
    if text_prefix_norm:
        matcher = SequenceMatcher(a=q_norm, b=text_prefix_norm, autojunk=False)
        kw_score = matcher.ratio()
    else:
        kw_score = 0.0
    
    # 表名匹配增强
    table_keywords = extract_table_keywords(query)
    table_boost = 0.0
    if table_keywords:
        for keyword in table_keywords:
            if keyword in text:
                boost = min(0.3, len(keyword) * 0.05)
                table_boost = max(table_boost, boost)
                print(f"    表名匹配: '{keyword}' -> boost={boost:.3f}")
    
    # 综合得分 (kw_weight = 0.3)
    kw_weight = 0.3
    base_score = (1 - kw_weight) * vec_score + kw_weight * kw_score
    final_score = min(1.0, base_score + table_boost)
    
    return final_score, vec_score, kw_score, table_boost

def test_table_matching():
    """测试表名匹配增强"""
    
    # 初始化向量存储
    row_vs = ChromaVectorStore(collection_name="row_vectors")
    
    # 测试查询
    question = "自营资金业务余额表中哪些字段的默认值是99991231"
    print(f"测试查询: {question}")
    
    # 提取表名关键词
    table_keywords = extract_table_keywords(question)
    print(f"提取的表名关键词: {table_keywords}")
    
    # 生成查询向量
    embedding = embed_texts([question])[0]
    
    # 进行向量召回
    print("\n=== 向量召回测试 ===")
    row_ret = row_vs.collection.query(
        query_embeddings=[embedding],
        n_results=1000,  # 召回更多结果以便测试
        include=["metadatas", "distances"],
    )
    
    if not row_ret.get("ids"):
        print("未召回到任何行向量")
        return
        
    row_ids = row_ret["ids"][0]
    row_metas = row_ret["metadatas"][0]
    row_dists = row_ret["distances"][0]
    
    # 计算增强得分
    enhanced_results = []
    for rid, meta, dist in zip(row_ids, row_metas, row_dists):
        sheet_id = meta.get("sheet_id")
        text = meta.get("text", "")
        rowid = meta.get("rowid")
        
        final_score, vec_score, kw_score, table_boost = calculate_enhanced_score(text, question, dist)
        
        enhanced_results.append({
            "id": rid,
            "sheet_id": sheet_id,
            "rowid": rowid,
            "text": text,
            "distance": dist,
            "vec_score": vec_score,
            "kw_score": kw_score,
            "table_boost": table_boost,
            "final_score": final_score
        })
    
    # 按增强得分排序
    enhanced_results.sort(key=lambda x: -x["final_score"])
    
    print(f"\n=== 增强后的排序结果 (前15名) ===")
    sheet_68_found = False
    sheet_68_with_default = []
    # 查找所有sheet_id=68且包含99991231的行
    all_sheet_68_with_default = []
    for result in enhanced_results:
        if result["sheet_id"] == 68 and "99991231" in result["text"]:
            if "数据项名称:" in result["text"]:
                field_name = result["text"].split("数据项名称:")[1].split("|")[0].strip()
                all_sheet_68_with_default.append(field_name)

    for i, result in enumerate(enhanced_results[:15]):
        sheet_id = result["sheet_id"]
        text = result["text"]
        final_score = result["final_score"]
        vec_score = result["vec_score"]
        kw_score = result["kw_score"]
        table_boost = result["table_boost"]
        
        print(f"\n{i+1}. sheet_id={sheet_id}, 最终得分={final_score:.4f}")
        print(f"   向量得分={vec_score:.4f}, 关键词得分={kw_score:.4f}, 表名加分={table_boost:.4f}")
        print(f"   文本: {text[:80]}...")

        if sheet_id == 68:
            sheet_68_found = True
            print("   ✓ 这是我们要找的sheet_id=68的行！")
            # 检查是否包含默认值99991231
            if "99991231" in text:
                print("   🎯 包含默认值99991231！")
                # 提取字段名
                if "数据项名称:" in text:
                    field_name = text.split("数据项名称:")[1].split("|")[0].strip()
                    sheet_68_with_default.append(field_name)
            print(f"   完整文本: {text}")
    
    if sheet_68_found:
        print(f"\n✓ 成功！sheet_id=68的行进入了前15名")
        if all_sheet_68_with_default:
            print(f"\n🎯 所有默认值为99991231的字段:")
            for field in set(all_sheet_68_with_default):  # 去重
                print(f"   - {field}")
            print(f"\n总共找到 {len(set(all_sheet_68_with_default))} 个字段")
    else:
        print(f"\n❌ sheet_id=68的行仍未进入前15名")

        # 查找sheet_id=68的行在增强后的排名
        for i, result in enumerate(enhanced_results):
            if result["sheet_id"] == 68:
                print(f"   sheet_id=68的行在增强后排名第{i+1}位")
                print(f"   得分: {result['final_score']:.4f} (向量={result['vec_score']:.4f}, 关键词={result['kw_score']:.4f}, 表名加分={result['table_boost']:.4f})")
                break

if __name__ == "__main__":
    test_table_matching()
