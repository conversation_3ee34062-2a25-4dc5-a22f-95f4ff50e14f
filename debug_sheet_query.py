#!/usr/bin/env python3
"""
调试脚本：查询数据库中包含"自营资金业务余额表"的sheet信息
"""
import sqlite3
import sys

def query_sheets_with_keyword(db_path="output/data.db", keyword="自营资金业务余额表"):
    """查询包含指定关键词的sheet信息"""
    try:
        conn = sqlite3.connect(db_path)
        
        # 查询sheet_metadata表
        print(f"=== 查询包含'{keyword}'的sheet信息 ===")
        cur = conn.execute("""
            SELECT sheet_id, sheet_name, table_name 
            FROM sheet_metadata 
            WHERE sheet_name LIKE ? OR table_name LIKE ?
            ORDER BY sheet_id
        """, (f"%{keyword}%", f"%{keyword}%"))
        
        sheets = cur.fetchall()
        if sheets:
            print("找到的sheet:")
            for sheet_id, sheet_name, table_name in sheets:
                print(f"  sheet_id: {sheet_id}, sheet_name: {sheet_name}, table_name: {table_name}")
        else:
            print("未找到匹配的sheet")
            
        # 查询所有sheet，看看有哪些
        print(f"\n=== 所有sheet列表（前20个）===")
        cur = conn.execute("""
            SELECT sheet_id, sheet_name, table_name 
            FROM sheet_metadata 
            ORDER BY sheet_id
            LIMIT 20
        """)
        
        all_sheets = cur.fetchall()
        for sheet_id, sheet_name, table_name in all_sheets:
            print(f"  {sheet_id}: {sheet_name} -> {table_name}")
            
        # 查询sheet_id=68的信息
        print(f"\n=== sheet_id=68的信息 ===")
        cur = conn.execute("""
            SELECT sheet_id, sheet_name, table_name 
            FROM sheet_metadata 
            WHERE sheet_id = 68
        """)
        
        sheet_68 = cur.fetchone()
        if sheet_68:
            sheet_id, sheet_name, table_name = sheet_68
            print(f"  sheet_id: {sheet_id}, sheet_name: {sheet_name}, table_name: {table_name}")
            
            # 查询该sheet的row_vectors样本
            print(f"\n=== sheet_id=68的row_vectors样本 ===")
            cur = conn.execute("""
                SELECT rowid, text 
                FROM row_vectors 
                WHERE sheet_id = 68
                LIMIT 3
            """)
            
            rows = cur.fetchall()
            for rowid, text in rows:
                print(f"  rowid {rowid}: {text[:100]}...")
        else:
            print("  未找到sheet_id=68")
            
        conn.close()
        
    except Exception as e:
        print(f"查询出错: {e}")

def query_default_values(db_path="output/data.db", sheet_id=68, default_value="99991231"):
    """查询指定sheet中包含特定默认值的字段"""
    try:
        conn = sqlite3.connect(db_path)

        print(f"=== 查询sheet_id={sheet_id}中默认值为{default_value}的字段 ===")
        cur = conn.execute("""
            SELECT rowid, text
            FROM row_vectors
            WHERE sheet_id = ? AND text LIKE ?
            ORDER BY rowid
        """, (sheet_id, f"%{default_value}%"))

        rows = cur.fetchall()
        if rows:
            print(f"找到{len(rows)}个包含默认值{default_value}的字段:")
            for rowid, text in rows:
                print(f"  rowid {rowid}: {text}")
        else:
            print(f"未找到包含默认值{default_value}的字段")

        conn.close()

    except Exception as e:
        print(f"查询出错: {e}")

if __name__ == "__main__":
    query_sheets_with_keyword()
    print("\n" + "="*80 + "\n")
    query_default_values()
