#!/usr/bin/env python3
"""
测试row vector recall的表名匹配改进
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.qa.pipeline import Q<PERSON><PERSON>eline

def test_row_recall():
    """测试改进后的row召回效果"""
    
    # 初始化QA pipeline
    qa = QAPipeline()
    
    # 测试问题：明确指定了"自营资金业务余额表"
    question = "自营资金业务余额表中哪些字段的默认值是99991231"
    
    print(f"测试问题: {question}")
    print("="*80)
    
    # 运行QA流程
    try:
        qa.run_question(question)
    except Exception as e:
        print(f"运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_row_recall()
