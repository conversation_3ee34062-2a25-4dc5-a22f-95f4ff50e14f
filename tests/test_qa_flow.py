import sys
from pathlib import Path as _P

# 确保项目根目录加入 sys.path，避免 "import src" 失败
ROOT = _P(__file__).resolve().parents[1]
if str(ROOT) not in sys.path:
    sys.path.insert(0, str(ROOT))

import sqlite3
import pytest  # type: ignore
import pandas as pd  # type: ignore

from src.mcp import MCPClient, QueryTable
from src.orchestration.dynamic_orchestrator import DynamicOrchestrator


# ---------------------------------------------------------------------------
# Dummy implementations 用于隔离依赖、加速测试
# ---------------------------------------------------------------------------

class DummyVectorizer:  # noqa: D101
    def embed_texts(self, texts):  # type: ignore[override]
        # 为所有文本返回零向量，保证 query 行为受 DummyVectorStore 控制
        return [[0.0] * 3 for _ in texts]


class DummyVectorStore:  # noqa: D101
    """返回固定 ID 集合，避免依赖真实 Chroma。"""

    def add_embeddings(self, ids, embeddings, metadata):  # noqa: D401
        pass

    # 根据传入 filter 返回伪造 id 列表
    def query(self, embedding, top_k=10, filter=None):  # noqa: D401
        if filter == {"level": "sheet"}:
            return ["sheet:1"]
        if filter and "sheet_id" in filter:
            return ["1:10", "1:11"]
        return []

    def delete(self, ids):  # noqa: D401
        pass


# ---------------------------------------------------------------------------
# Fixtures
# ---------------------------------------------------------------------------


@pytest.fixture()
def temp_db(tmp_path: _P):
    """创建临时 SQLite 数据库，含示例表。"""
    db_path = tmp_path / "qa.db"
    conn = sqlite3.connect(db_path)
    conn.execute("CREATE TABLE tbl (col TEXT)")
    conn.execute("INSERT INTO tbl VALUES ('x')")
    conn.commit()
    conn.close()
    return db_path


# ---------------------------------------------------------------------------
# Tests
# ---------------------------------------------------------------------------

def test_mcp_query_table_basic(temp_db: _P):
    client = MCPClient(db_path=str(temp_db))
    qt = QueryTable(table_name="tbl", columns=["col"], filter={"col": "= 'x'"})  # type: ignore[arg-type]
    df = client.query_table(qt)
    assert not df.empty
    assert df.iloc[0]["col"] == "x"


def test_orchestrator_answer(temp_db: _P):
    client = MCPClient(db_path=str(temp_db))
    vs = DummyVectorStore()
    vz = DummyVectorizer()

    orc = DynamicOrchestrator()

    # ---- monkeypatch VectorRecallComponent ----
    from src.orchestration.components.vector_recall_component import VectorRecallComponent  # noqa: E402

    def _setup_dummy(self, cfg):  # type: ignore[no-self-argument]
        self.vectorizer = vz  # type: ignore[attr-defined]
        self.store = vs  # type: ignore[attr-defined]
        self.topk = cfg.get("topk", 3)

    VectorRecallComponent.setup = _setup_dummy  # type: ignore[assignment]

    # ---- monkeypatch MCPQueryComponent ----
    from src.orchestration.components.mcp_query_component import MCPQueryComponent  # noqa: E402

    def _run_mock(self, payload, **kw):  # type: ignore[no-self-argument]
        return {"tables_preview": {"tbl": [{"col": "x"}]}}

    MCPQueryComponent.run = _run_mock  # type: ignore[assignment]

    # 导入 intent_classifier_component 以确保注册
    import src.orchestration.components.intent_classifier_component  # noqa: E402,F401
    import src.orchestration.components.llm_answer_component  # noqa: E402,F401

    result = orc.answer("测试问题")
    assert "tables_preview" in result
    tbl_preview = result["tables_preview"]["tbl"]
    assert tbl_preview[0]["col"] == "x" 