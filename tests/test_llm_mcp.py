"""Integration test: verify <PERSON><PERSON> chooses <PERSON><PERSON> render action.

运行前提：本地/远端已部署支持 Function-Calling 或结构化输出的大模型，且
`env LLM_PROVIDER=ollama` (或 siliconflow) 已配置。

如需跳过，可设置环境变量 `SKIP_LLM_TESTS=1`。
"""

from __future__ import annotations

import os
import sys
from pathlib import Path

# Ensure project root is in PYTHONPATH so that `import src.` works when running tests directly.
ROOT_DIR = Path(__file__).resolve().parents[1]
if str(ROOT_DIR) not in sys.path:
    sys.path.insert(0, str(ROOT_DIR))

import pytest


# ----------------------------------------------------------------------
# Skip logic
# ----------------------------------------------------------------------


if os.getenv("SKIP_LLM_TESTS") == "1":
    pytest.skip("SKIP_LLM_TESTS env var set", allow_module_level=True)


from src.parser.llm.client import ask  # noqa: E402  pylint: disable=wrong-import-position


SYSTEM_PROMPT = """你是一个工具选择器。我们有如下工具：\n\n1. query_table: 用于精确查询数据库表；需要参数 table_name / columns / filter / limit。\n2. render_direct: 如果已经有 DataFrame 并且行数较少，直接将其渲染为 markdown/json。\n3. render_summary: 当 DataFrame 较大或只需要摘要时，用此工具生成摘要。\n\n根据用户需求，如果需要对表格做摘要，请调用 render_summary。输出 JSON，不要有额外文本。"""


def test_llm_returns_render_summary() -> None:  # noqa: D401
    """给定提示，大模型应返回包含 render_summary action 的 JSON。"""

    user_prompt = "用户问题：请总结 Loans 表的主要列信息。若需要摘要请调用 render_summary。"

    response = ask(user_prompt, system=SYSTEM_PROMPT, temperature=0)

    # 允许模型返回可解析 JSON 或嵌入自然语言中的 JSON。这里只做包含判断。
    assert "render_summary" in response, f"LLM unexpected output: {response[:200]}"  # noqa: S101 