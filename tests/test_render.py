"""Tests for MCP render capabilities."""

from __future__ import annotations

import json
import re

import pandas as pd  # type: ignore


from src.mcp.client import MC<PERSON><PERSON>, RenderDirect, RenderSummary


# ----------------------------------------------------------------------
# Helper fixtures
# ----------------------------------------------------------------------


def _sample_df() -> pd.DataFrame:  # noqa: D401
    return pd.DataFrame({
        "id": [1, 2, 3],
        "name": ["<PERSON>", "<PERSON>", "<PERSON>"],
        "score": [85, 92, 78],
    })


# ----------------------------------------------------------------------
# Tests
# ----------------------------------------------------------------------


def test_render_direct_markdown() -> None:  # noqa: D401
    df = _sample_df()
    rd = RenderDirect(dataframe=df, style="markdown", max_rows=5)
    output = MCPClient.render_direct(rd)

    # Markdown header row should include id, name, score columns regardless of spacing
    header_line = output.split("\n", 1)[0]
    assert re.search(r"\|\s*id\s*\|", header_line, re.IGNORECASE)
    assert re.search(r"\|\s*name\s*\|", header_line, re.IGNORECASE)
    assert re.search(r"\|\s*score\s*\|", header_line, re.IGNORECASE)


def test_render_direct_json_execute() -> None:  # noqa: D401
    df = _sample_df()
    client = MCPClient(db_path=":memory:")
    payload = {
        "action": "render_direct",
        "dataframe": df,
        "style": "json",
        "max_rows": 10,
    }
    output = client.execute(payload)
    data = json.loads(output)
    assert isinstance(data, list)
    assert data[0]["name"] == "Alice"


def test_render_summary_contains_metadata() -> None:  # noqa: D401
    df = _sample_df()
    rs = RenderSummary(dataframe=df, table_name="test_table", max_cols=2)
    summary = MCPClient.render_summary(rs)
    # Should mention table name and number of rows
    assert "test_table" in summary
    assert "行数 3" in summary or "行数：3" in summary 