"""
Schema Analyzer: A script to intelligently analyze the database schema using an LLM.

This script iterates through all tables in the database, sends sample data to an LLM,
and asks the LLM to identify which columns represent the 'name' and 'format' of data items.
The results are saved to a JSON file, which is then used by the MetadataIndexer to
build its search index without hardcoded column names.
"""
import json
from pathlib import Path
import pandas as pd
from sqlalchemy import create_engine, inspect
from src.parser.llm.client import ask, _extract_json_block
from src.utils.config import get_task_cfg

def analyze_schema():
    """
    Analyzes the database schema to identify name and format columns for each table.
    """
    print("Starting schema analysis...")

    task_cfg = get_task_cfg("schema_analysis")
    db_path = "output/data.db"  # This can be made configurable
    engine = create_engine(f"sqlite:///{db_path}")
    inspector = inspect(engine)
    
    schema_mapping = {}
    
    for table_name in inspector.get_table_names():
        try:
            # Fetch sample data
            df = pd.read_sql_table(table_name, engine,
                                   columns=[c["name"] for c in inspector.get_columns(table_name)],
                                   coerce_float=False)
            sample_data = df.head().to_markdown(index=False)
            
            prompt = f"Table: {table_name}\n\nSample Data:\n{sample_data}"
            
            # Ask the LLM to identify the columns
            raw_response = ask(
                prompt=prompt,
                system=task_cfg.get("system_prompt"),
                provider=task_cfg.get("provider"),
                model=task_cfg.get("model"),
                think=task_cfg.get("think"),
                response_format={"type": "json_object"}
            )
            
            json_response = _extract_json_block(raw_response)
            column_mapping = json.loads(json_response)
            
            if column_mapping.get("name_column"):
                schema_mapping[table_name] = column_mapping
                print(f"  - Analyzed table '{table_name}': {column_mapping}")

        except Exception as e:
            print(f"  - Could not analyze table '{table_name}': {e}")
            continue

    output_path = Path("config/schema_mapping.json")
    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(schema_mapping, f, indent=2, ensure_ascii=False)

    print(f"\nSchema analysis complete. Mapping saved to {output_path}")

if __name__ == "__main__":
    analyze_schema() 