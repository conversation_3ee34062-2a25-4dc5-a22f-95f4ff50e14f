#!/usr/bin/env python3
"""
测试严格的表名匹配策略
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.orchestration.components.row_vector_recall_component import RowVectorRecallComponent
from src.vectorization.vector_store import ChromaVectorStore

def test_strict_table_matching():
    """测试严格的表名匹配策略"""
    
    # 初始化组件
    component = RowVectorRecallComponent()
    config = {
        "top_k_rows": 20,
        "top_n_rows": 5,
        "row_collection": "row_vectors",
        "sheet_collection": "sheet_vectors",
        "kw_weight": 0.3
    }
    component.setup(config)

    # 测试查询
    question = "自营资金业务余额表中哪些字段的默认值是99991231,这是一个 row intent"
    print(f"测试查询: {question}")

    # 执行召回
    payload = {
        "query": question,
        "intent": "row"
    }
    result = component.run(payload)
    
    print(f"\n召回结果:")
    row_texts = result.get("row_texts", [])
    print(f"总共召回 {len(row_texts)} 行")

    # 按sheet_id分组显示
    sheet_groups = {}
    for row in row_texts:
        sheet_id = row.get("sheet_id")
        if sheet_id not in sheet_groups:
            sheet_groups[sheet_id] = []
        sheet_groups[sheet_id].append(row)
    
    print(f"\n按sheet_id分组:")
    for sheet_id, rows in sheet_groups.items():
        print(f"\nsheet_id={sheet_id}: {len(rows)}个行")
        
        for i, row in enumerate(rows[:3]):  # 只显示前3个
            rowid = row.get("rowid")
            text = row.get("text", "")
            print(f"  {i+1}. rowid={rowid}")
            print(f"     文本: {text[:100]}...")
            
            # 检查是否包含99991231
            if "99991231" in text:
                print(f"     🎯 包含默认值99991231！")
    
    # 特别关注是否只有sheet_id=68的结果
    if len(sheet_groups) == 1 and 68 in sheet_groups:
        print(f"\n🎉 成功！只召回了目标表sheet_id=68的行")
        
        # 显示所有包含99991231的字段
        target_fields = []
        for row in sheet_groups[68]:
            text = row.get("text", "")
            if "99991231" in text:
                # 提取字段名
                if "数据项名称:" in text:
                    field_name = text.split("数据项名称:")[1].split("|")[0].strip()
                    target_fields.append(field_name)
        
        print(f"\n🎯 找到的目标字段:")
        for field in target_fields:
            print(f"  - {field}")
            
    elif 68 not in sheet_groups:
        print(f"\n❌ 失败！没有召回目标表sheet_id=68的行")
    else:
        print(f"\n⚠️  部分成功！召回了目标表sheet_id=68的行，但也包含了其他表的行")
        print(f"   召回的sheet_ids: {list(sheet_groups.keys())}")

if __name__ == "__main__":
    test_strict_table_matching()
