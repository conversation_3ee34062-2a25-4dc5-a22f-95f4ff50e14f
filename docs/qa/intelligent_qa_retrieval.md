# 智能问答的信息召回方案设计

> 适用版本：v1.0+

本文档说明最新版 QA Pipeline 如何利用 **意图识别 + 分层向量检索 + 关键词重排 + LLM 汇总** 完成“既聪明又靠谱”的问答。

当前默认 Pipeline：

```
intent_classifier → vector_recall → rerank → llm_answer
```

所有步骤均在 `config/qa_pipeline.yaml` 的 `fixed_steps` 字段中声明，可按需增删。

---

## 1. 背景

数据侧已经具备两条互补能力：
1. **分层向量索引**：Workbook / Sheet / Row 多粒度切片写入 Chroma，支持语义检索。
2. **精准数据查询（MCP 封装）**：直接命中真实数据库，100% 准确、无幻觉。

新版 Pipeline 先通过向量召回定位上下文，再交由 LLM 汇总；如需结构化数据，可插拔 `mcp_query` 组件，但默认未启用。

---

## 2. 意图识别 & 层级决策

固定节点 **IntentClassifierComponent** 输出：

| 字段 | 含义 | 示例 |
| ---- | ---- | ---- |
| `intent` | 归一化意图名 | `ask_sheet_meta` / `numeric_query` |
| `strategy` | 召回策略 | `vector_sheet` / `mcp_only` |
| `level_probs` | `{file,sheet,row}` 概率分布 | `{"file":0.1,"sheet":0.7,"row":0.2}` |
| `strategy_params` | 细粒度控制（如 `min_prob`、`level`） | `{"min_prob":0.4}` |

流程：
1. 先按 `config/intent_rules.yaml` 关键词匹配；未命中再走 LLM 推理。
2. 若仍无法判定，回退 `unknown`，并采用 `vector_sheet` 策略。
3. `level_probs` 供 **VectorRecallComponent** 调整多层检索权重。

---

## 3. 总体流程

```mermaid
graph TD
    Q[用户问题] --> IC[IntentClassifier]
    IC --> VR[VectorRecall]
    VR --> RR[Rerank]
    RR --> LA[LLMAnswer]
    LA --> A[回复用户]
    A -->|澄清/追问| Q
```

> 可选：在 VectorRecall→LA 之间插入 `mcp_query`（结构化查询）或 `sheet_meta`（元数据补全）等组件，完全由 YAML 配置驱动。

---

## 4. 分层向量召回策略（VectorRecallComponent）

- 同时连接 `file_vectors`、`sheet_vectors`、`row_vectors` 三个 collection。
- 接收 `level_probs`，对低于 `min_prob` 的层级直接跳过检索，节省开销。
- 计算加权得分并合并去重后返回：
  - `sheet_ids`：下游可据此获取表元数据或查询行文本。
  - `file_texts` / `sheet_meta` / `row_texts`：直接拼接文本，减少 DB 依赖。
- `topk` 和 `embedding_model` 等参数由 YAML 配置，可动态调整。

> 行级问题场景可切换 `row_vector_recall`（一次完成行召回+重排），但 QA 默认关闭。

---

## 5. 关键词重排（RerankComponent）

- 对 `row_texts` 按关键 token 匹配次数排序，保留 `top_k`（默认 5）。
- 支持远程 SiliconFlow Rerank 服务（配置 `provider: siliconflow` 与 `model`）。

---

## 6. LLM 汇总（LLMAnswerComponent）

- 收集问题、行文本、Sheet 描述、File 文本、可选 SQL 结果等上下文。
- 根据 `level_probs` 动态调整上下文顺序，优先高概率层级。
- 支持两种模式：
  1. **结构化输出**：`llm_decide_preview: true` 时让 LLM 返回 `{"answer": str, "show_preview": bool, ...}`。
  2. **纯文本汇总**：默认走 `summarize_answer()` 简洁回答。
- 回答长度、System Prompt、是否启用思考链由 YAML 控制。

---

## 7. 组件列表（默认启用）

| 组件 | 主要输出 | 说明 |
| ---- | -------- | ---- |
| `intent_classifier` | `intent`, `strategy`, `level_probs` | 规则+LLM 双通道分类 |
| `vector_recall` | `sheet_ids`, `row_texts` 等 | 多层级语义检索 |
| `rerank` | `row_texts` | 关键词或远程 Rerank 服务重新排序 |
| `llm_answer` | `answer` | 汇总上下文生成最终回答 |

> 扩展组件：`mcp_query`、`sheet_meta`、`column_lookup`、`row_lookup` 等，可按业务需求加入 `fixed_steps`。

---

## 8. 多轮交互要点

1. **上下文缓存**：VectorRecall 结果可缓存，追问时增量扩展检索范围。
2. **结果校验**：让 LLM 先生成 MCP 查询，再对比自身回答中的数值，降低幻觉。
3. **澄清回合**：当 `answer` 不确定或缺少上下文时，可由 LLM 询问用户补充信息后重走流程。

---

## 9. 下一步工作

1. 在 Chroma 中引入 **表格块** 级向量，进一步提升定位精度。
2. 为 `mcp_query` 设计通用 Schema 并补充单元测试。
3. 引入 Streaming Token 技术，降低 LLMAnswer 延迟。

---

> 如对方案有任何疑问或改进建议，欢迎在 `docs/` 提 PR 或 Issue 讨论。 