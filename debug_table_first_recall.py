#!/usr/bin/env python3
"""
调试表名优先召回策略的具体结果
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.vectorization.vectorizer import embed_texts
from src.vectorization.vector_store import ChromaVectorStore
import re

def debug_table_first_recall():
    """调试表名优先召回策略"""
    
    # 初始化向量存储
    row_vs = ChromaVectorStore(collection_name="row_vectors")
    
    # 测试查询
    question = "自营资金业务余额表中哪些字段的默认值是99991231"
    print(f"测试查询: {question}")
    
    # 提取表名关键词
    def _extract_table_keywords(query: str) -> list[str]:
        """从查询中提取可能的表名关键词"""
        # 常见的表名模式
        table_patterns = [
            r"(\w*表)",  # 以"表"结尾的词
            r"(\w*信息表)",  # 以"信息表"结尾的词
            r"(\w*业务\w*表)",  # 包含"业务"的表名
            r"(\w*余额表)",  # 以"余额表"结尾的词
            r"(\w*明细表)",  # 以"明细表"结尾的词
        ]
        
        keywords = []
        for pattern in table_patterns:
            matches = re.findall(pattern, query)
            keywords.extend(matches)
        
        # 去重并过滤掉过短的关键词
        keywords = list(set([kw for kw in keywords if len(kw) >= 3]))
        return keywords

    table_keywords = _extract_table_keywords(question)
    print(f"提取的表名关键词: {table_keywords}")
    
    # 生成查询向量
    embedding = embed_texts([question])[0]
    
    # 模拟表名优先召回策略
    print("\n=== 表名优先召回策略 ===")
    
    # 先召回大量结果用于表名匹配
    large_k = 1000
    row_ret = row_vs.collection.query(
        query_embeddings=[embedding],
        n_results=large_k,
        include=["metadatas", "distances"],
    )
    
    if row_ret.get("ids"):
        # 筛选出包含目标表名的行
        filtered_results = []
        all_ids = row_ret["ids"][0]
        all_metas = row_ret["metadatas"][0]
        all_dists = row_ret["distances"][0]
        
        for rid, meta, dist in zip(all_ids, all_metas, all_dists):
            text = meta.get("text", "")
            sheet_id = meta.get("sheet_id")
            rowid = meta.get("rowid")
            
            # 检查是否包含任何表名关键词
            for keyword in table_keywords:
                if keyword in text:
                    filtered_results.append({
                        "id": rid,
                        "sheet_id": sheet_id,
                        "rowid": rowid,
                        "text": text,
                        "distance": dist
                    })
                    break
        
        print(f"表名匹配筛选: {len(all_ids)} 总数 -> {len(filtered_results)} 匹配")
        
        # 按sheet_id分组显示
        sheet_groups = {}
        for result in filtered_results:
            sheet_id = result["sheet_id"]
            if sheet_id not in sheet_groups:
                sheet_groups[sheet_id] = []
            sheet_groups[sheet_id].append(result)
        
        print(f"\n按sheet_id分组的匹配结果:")
        for sheet_id, results in sheet_groups.items():
            print(f"\nsheet_id={sheet_id}: {len(results)}个匹配行")
            
            # 显示前3个结果
            for i, result in enumerate(results[:3]):
                rowid = result["rowid"]
                text = result["text"]
                distance = result["distance"]
                print(f"  {i+1}. rowid={rowid}, 距离={distance:.4f}")
                print(f"     文本: {text[:100]}...")
                
                # 检查是否包含99991231
                if "99991231" in text:
                    print(f"     🎯 包含默认值99991231！")
        
        # 特别关注sheet_id=68的结果
        if 68 in sheet_groups:
            print(f"\n🎯 重点关注sheet_id=68的结果:")
            sheet_68_results = sheet_groups[68]
            for i, result in enumerate(sheet_68_results):
                rowid = result["rowid"]
                text = result["text"]
                distance = result["distance"]
                print(f"\n  {i+1}. rowid={rowid}, 距离={distance:.4f}")
                print(f"     完整文本: {text}")
                
                if "99991231" in text:
                    print(f"     🎯 包含默认值99991231！")
        else:
            print(f"\n❌ 没有找到sheet_id=68的匹配结果")

if __name__ == "__main__":
    debug_table_first_recall()
