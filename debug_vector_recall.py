#!/usr/bin/env python3
"""
调试向量召回：测试为什么没有召回到sheet_id=68的行
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.vectorization.vectorizer import embed_texts
from src.vectorization.vector_store import ChromaVectorStore

def test_vector_recall():
    """测试向量召回"""
    
    # 初始化向量存储
    row_vs = ChromaVectorStore(collection_name="row_vectors")
    
    # 测试查询
    question = "自营资金业务余额表中哪些字段的默认值是99991231"
    print(f"测试查询: {question}")
    
    # 生成查询向量
    embedding = embed_texts([question])[0]
    print(f"查询向量维度: {len(embedding)}")
    
    # 进行向量召回
    print("\n=== 向量召回测试 ===")
    row_ret = row_vs.collection.query(
        query_embeddings=[embedding],
        n_results=20,  # 召回更多结果
        include=["metadatas", "distances"],
    )
    
    if not row_ret.get("ids"):
        print("未召回到任何行向量")
        return
        
    row_ids = row_ret["ids"][0]
    row_metas = row_ret["metadatas"][0]
    row_dists = row_ret["distances"][0]
    
    print(f"召回了 {len(row_ids)} 个结果")
    
    # 检查是否包含sheet_id=68的行
    sheet_68_found = False
    for i, (rid, meta, dist) in enumerate(zip(row_ids, row_metas, row_dists)):
        sheet_id = meta.get("sheet_id")
        text = meta.get("text", "")
        
        if sheet_id == 68:
            sheet_68_found = True
            print(f"\n✓ 找到sheet_id=68的行:")
            print(f"  排名: {i+1}")
            print(f"  ID: {rid}")
            print(f"  距离: {dist:.4f}")
            print(f"  文本: {text[:100]}...")
        
        # 显示前10个结果
        if i < 10:
            print(f"\n{i+1}. sheet_id={sheet_id}, 距离={dist:.4f}")
            print(f"   文本: {text[:80]}...")
    
    if not sheet_68_found:
        print(f"\n❌ 在前{len(row_ids)}个结果中未找到sheet_id=68的行")
        
        # 让我们专门查询包含"自营资金业务余额表"的行
        print("\n=== 专门查询包含'自营资金业务余额表'的行 ===")
        all_ret = row_vs.collection.query(
            query_embeddings=[embedding],
            n_results=100,  # 召回更多结果
            include=["metadatas", "distances"],
        )
        
        if all_ret.get("ids"):
            all_ids = all_ret["ids"][0]
            all_metas = all_ret["metadatas"][0]
            all_dists = all_ret["distances"][0]
            
            for i, (rid, meta, dist) in enumerate(zip(all_ids, all_metas, all_dists)):
                text = meta.get("text", "")
                if "自营资金业务余额表" in text:
                    sheet_id = meta.get("sheet_id")
                    print(f"\n找到包含'自营资金业务余额表'的行:")
                    print(f"  排名: {i+1}")
                    print(f"  sheet_id: {sheet_id}")
                    print(f"  距离: {dist:.4f}")
                    print(f"  文本: {text[:150]}...")
                    break
            else:
                print("在前100个结果中也未找到包含'自营资金业务余额表'的行")

def check_sheet_68_vectors():
    """检查sheet_id=68的向量是否存在"""
    print("\n=== 检查sheet_id=68的向量是否存在 ===")

    row_vs = ChromaVectorStore(collection_name="row_vectors")

    # 尝试获取所有数据来检查
    try:
        # 使用一个随机向量来获取所有数据
        dummy_embedding = [0.0] * 2560  # 使用与实际向量相同的维度
        all_ret = row_vs.collection.query(
            query_embeddings=[dummy_embedding],
            n_results=10000,  # 获取大量结果
            include=["metadatas"],
        )

        if all_ret.get("ids"):
            all_metas = all_ret["metadatas"][0]

            sheet_68_count = 0
            for meta in all_metas:
                if meta.get("sheet_id") == 68:
                    sheet_68_count += 1
                    if sheet_68_count <= 3:  # 只显示前3个
                        text = meta.get("text", "")
                        rowid = meta.get("rowid")
                        print(f"  找到sheet_id=68的行: rowid={rowid}")
                        print(f"    文本: {text[:100]}...")

            print(f"\n总共找到 {sheet_68_count} 个sheet_id=68的行")

            if sheet_68_count == 0:
                print("❌ 向量数据库中没有sheet_id=68的数据！")
            else:
                print("✓ 向量数据库中存在sheet_id=68的数据")

        else:
            print("无法获取向量数据库中的数据")

    except Exception as e:
        print(f"检查向量数据库时出错: {e}")

if __name__ == "__main__":
    test_vector_recall()
    check_sheet_68_vectors()
