[project]
name = "hierarchical-data-server"
version = "0.1.0"
requires-python = ">=3.13"
dependencies = [
    "aiosqlite>=0.21.0",
    "chromadb>=1.0.15",
    "fastapi>=0.116.0",
    "openpyxl>=3.1.5",
    "pandas>=2.2.0",
    "requests>=2.31.0",
    "pytest>=8.2.0",
    "rich>=13.7.0",
    "redis>=6.2.0",
    "sqlalchemy[asyncio]>=2.0.41",
    "uvicorn[standard]>=0.35.0",
    "tiktoken>=0.5.1",
    "pyyaml>=6.0.0",
    "chroma>=0.2.0",
    "rapidfuzz>=3.6"
]

[project.scripts]
ingest-excel = "ingestion.cli:main"
qa-console = "qa.pipeline:main"
analyze-schema = "scripts.analyze_schema:analyze_schema"
