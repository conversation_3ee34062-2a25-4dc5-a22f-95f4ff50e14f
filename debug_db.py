#!/usr/bin/env python3
"""
简单的数据库调试脚本
"""
import sqlite3
import os

def main():
    # 检查数据库文件是否存在
    db_path = 'output/data.db'
    if os.path.exists(db_path):
        print(f'数据库文件存在: {db_path}')
        conn = sqlite3.connect(db_path)
        
        # 检查主要表的记录数
        tables = ['file_metadata', 'sheet_metadata', 'row_data', 'sheet_attributes', 'row_attributes']
        for table in tables:
            try:
                cursor = conn.execute(f'SELECT COUNT(*) FROM {table}')
                count = cursor.fetchone()[0]
                print(f'{table}: {count} 条记录')
            except Exception as e:
                print(f'{table}: 表不存在或查询失败 - {e}')
        
        # 检查是否有关于自营资金业务余额表的数据
        try:
            cursor = conn.execute('''
                SELECT sheet_id, sheet_name, table_name
                FROM sheet_metadata
                WHERE sheet_name LIKE '%自营资金%' OR table_name LIKE '%自营资金%'
                LIMIT 5
            ''')
            sheets = cursor.fetchall()
            if sheets:
                print('\n找到相关sheet:')
                for sheet_id, sheet_name, table_name in sheets:
                    print(f'  sheet_id: {sheet_id}, sheet_name: {sheet_name}, table_name: {table_name}')

                    # 检查这个sheet的属性中是否有默认值99991231的字段
                    cursor2 = conn.execute('''
                        SELECT key, value
                        FROM sheet_attributes
                        WHERE sheet_id = ? AND value LIKE '%99991231%'
                        LIMIT 10
                    ''', (sheet_id,))
                    attrs = cursor2.fetchall()
                    if attrs:
                        print(f'    sheet_id {sheet_id} 中包含99991231的属性:')
                        for key, value in attrs:
                            print(f'      {key}: {value[:200]}...' if len(value) > 200 else f'      {key}: {value}')
                    else:
                        print(f'    sheet_id {sheet_id} 中未找到包含99991231的属性')
            else:
                print('\n未找到包含"自营资金"的sheet')
        except Exception as e:
            print(f'查询sheet失败: {e}')
        
        # 检查sheet_attributes表中的向量数据
        try:
            cursor = conn.execute('''
                SELECT key, value
                FROM sheet_attributes
                WHERE sheet_id = 68
                LIMIT 10
            ''')
            attrs = cursor.fetchall()
            if attrs:
                print('\n自营资金业务余额表的属性:')
                for key, value in attrs:
                    print(f'  {key}: {value[:100]}...' if len(value) > 100 else f'  {key}: {value}')
            else:
                print('\n未找到sheet_id=68的属性')
        except Exception as e:
            print(f'查询sheet_attributes失败: {e}')

        # 检查整个数据库中有哪些字段的默认值是99991231
        print('\n检查整个数据库中包含99991231的字段:')
        try:
            cursor = conn.execute('''
                SELECT sa.sheet_id, sm.sheet_name, sa.key, sa.value
                FROM sheet_attributes sa
                JOIN sheet_metadata sm ON sa.sheet_id = sm.sheet_id
                WHERE sa.value LIKE '%99991231%'
                ORDER BY sa.sheet_id
                LIMIT 20
            ''')
            results = cursor.fetchall()
            if results:
                for sheet_id, sheet_name, key, value in results:
                    print(f'  Sheet {sheet_id} ({sheet_name}):')
                    print(f'    {key}: {value[:300]}...' if len(value) > 300 else f'    {key}: {value}')
                    print()
            else:
                print('  未找到任何包含99991231的字段')
        except Exception as e:
            print(f'查询失败: {e}')

        # 检查向量存储
        print('\n检查向量存储目录:')
        vector_dir = 'vector_store'
        if os.path.exists(vector_dir):
            files = os.listdir(vector_dir)
            print(f'向量存储文件: {files}')
        else:
            print('向量存储目录不存在')
        
        conn.close()
    else:
        print(f'数据库文件不存在: {db_path}')

if __name__ == "__main__":
    main()
